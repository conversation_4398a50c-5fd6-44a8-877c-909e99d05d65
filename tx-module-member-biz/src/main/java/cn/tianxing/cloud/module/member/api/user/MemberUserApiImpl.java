package cn.tianxing.cloud.module.member.api.user;

import cn.hutool.core.collection.CollectionUtil;
import cn.tianxing.cloud.framework.common.enums.CommonStatusEnum;
import cn.tianxing.cloud.framework.common.pojo.CommonResult;
import cn.tianxing.cloud.framework.common.pojo.PageParam;
import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.framework.common.util.object.BeanUtils;
import cn.tianxing.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tianxing.cloud.module.member.api.user.dto.MemberUserPageReqDTO;
import cn.tianxing.cloud.module.member.api.user.dto.MemberUserRespDTO;
import cn.tianxing.cloud.module.member.api.user.dto.MemberUserVerifyDTO;
import cn.tianxing.cloud.module.member.convert.user.MemberUserConvert;
import cn.tianxing.cloud.module.member.dal.dataobject.user.MemberUserDO;
import cn.tianxing.cloud.module.member.dal.mysql.user.MemberUserMapper;
import cn.tianxing.cloud.module.member.service.user.MemberUserService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.Collection;
import java.util.List;

import static cn.tianxing.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.tianxing.cloud.framework.common.pojo.CommonResult.success;
import static cn.tianxing.cloud.module.member.enums.ErrorCodeConstants.USER_MOBILE_NOT_EXISTS;

/**
 * 会员用户的 API 实现类
 *
 * <AUTHOR>
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class MemberUserApiImpl implements MemberUserApi {

    @Resource
    private MemberUserService userService;

    @Resource
    private MemberUserMapper memberUserMapper;

    @Override
    public CommonResult<MemberUserRespDTO> getUser(Long id) {
        MemberUserDO user = userService.getUser(id);
        return success(MemberUserConvert.INSTANCE.convert2(user));
    }

    @Override
    public CommonResult<List<MemberUserRespDTO>> getUserList(Collection<Long> ids) {
//        return success(MemberUserConvert.INSTANCE.convertList2(userService.getUserList(ids)));
        List<MemberUserDO> userList = userService.getUserList(ids);
        List<MemberUserRespDTO> memberUserRespDTOS = BeanUtils.toBean(userList, MemberUserRespDTO.class);
        return success(memberUserRespDTOS);
    }

    @Override
    public CommonResult<List<MemberUserRespDTO>> getUserListByNickname(String nickname) {
        return success(MemberUserConvert.INSTANCE.convertList2(userService.getUserListByNickname(nickname)));
    }

    @Override
    public CommonResult<MemberUserRespDTO> getUserByMobile(String mobile) {
        return success(MemberUserConvert.INSTANCE.convert2(userService.getUserByMobile(mobile)));
    }

    @Override
    public CommonResult<Boolean> validateUser(Long id) {
        MemberUserDO user = userService.getUser(id);
        if (user == null) {
            throw exception(USER_MOBILE_NOT_EXISTS);
        }
        return success(true);
    }

    @Override
    public CommonResult<Boolean> verify(MemberUserVerifyDTO memberUserVerifyDTO) {
        userService.updateUserVerify(memberUserVerifyDTO);
        return success(true);
    }

    @Override
    public CommonResult<List<MemberUserRespDTO>> getEnabledUsers() {
        List<MemberUserDO> users = memberUserMapper.selectList(MemberUserDO::getStatus, CommonStatusEnum.ENABLE.getStatus());
        return success(MemberUserConvert.INSTANCE.convertList2(users));
    }
    
    @Override
    public CommonResult<PageResult<MemberUserRespDTO>> getEnabledUsersPage(@Valid @RequestBody MemberUserPageReqDTO pageReqDTO) {
        // 构建查询条件
        LambdaQueryWrapperX<MemberUserDO> queryWrapper = new LambdaQueryWrapperX<MemberUserDO>()
                .eqIfPresent(MemberUserDO::getStatus, pageReqDTO.getStatus() != null ? pageReqDTO.getStatus() : CommonStatusEnum.ENABLE.getStatus())
                .likeIfPresent(MemberUserDO::getNickname, pageReqDTO.getNickname())
                .likeIfPresent(MemberUserDO::getMobile, pageReqDTO.getMobile());
        
        // 执行分页查询
        PageResult<MemberUserDO> pageResult = memberUserMapper.selectPage(pageReqDTO, queryWrapper);
        
        // 转换结果
        List<MemberUserRespDTO> memberUserRespDTOs = MemberUserConvert.INSTANCE.convertList2(pageResult.getList());
        return success(new PageResult<>(memberUserRespDTOs, pageResult.getTotal()));
    }

    @Override
    public CommonResult<PageResult<MemberUserRespDTO>> getFilterUsers(@RequestParam("pageNo") Integer pageNo, @RequestParam("pageSize") Integer pageSize, @RequestParam("businessStaffIdList") Collection<Long> userIds) {
        PageParam pageParam = new PageParam();
        pageParam.setPageNo(pageNo);
        pageParam.setPageSize(pageSize);
        // 分页查询会员列表
        PageResult<MemberUserDO> mapperFilterUserList = memberUserMapper.getFilterUsers(userIds, CommonStatusEnum.ENABLE.getStatus(), pageParam);
        List<MemberUserDO> users = mapperFilterUserList.getList();
        if (CollectionUtil.isEmpty(users)) {
            // 空处理
            return success(PageResult.empty());
        } else {
            // 数据组装
            List<MemberUserRespDTO> memberUserRespDTOS = MemberUserConvert.INSTANCE.convertList2(users);
            return success(new PageResult<>(memberUserRespDTOS, mapperFilterUserList.getTotal()));
        }
    }

}
