package cn.tianxing.cloud.module.member.dal.dataobject.card;

import cn.tianxing.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 会员卡开通方式 DO
 *
 * <AUTHOR>
 */
@TableName("member_card_activation")
@Schema(description = "会员卡开通方式")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberCardActivationDO extends BaseDO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 卡ID
     */
    @Schema(description = "卡ID，关联会员卡表")
    private Long cardId;

    /**
     * 开通条件
     */
    @Schema(description = "开通条件")
    private String openCondition;

    /**
     * 开通分组
     */
    @Schema(description = "分组")
    private Integer activationGroup;

    /**
     * 结束条件
     */
    @Schema(description = "结束条件")
    private String endCondition;

    /**
     * 类型
     */
    @Schema(description = "类型   0开通条件  1结束条件")
    private Integer type;
} 