package cn.tianxing.cloud.module.member.service.card;

import cn.tianxing.cloud.module.member.dal.dataobject.card.BenefitValueDO;
import java.util.List;
import java.util.Collection;

/**
 * 权益值详情 Service 接口
 */
public interface BenefitValueService {

    Long createBenefitValue(BenefitValueDO benefitValue);

    void updateBenefitValue(BenefitValueDO benefitValue);

    void deleteBenefitValue(Long id);

    BenefitValueDO getBenefitValue(Long id);

    List<BenefitValueDO> getBenefitValueList(Collection<Long> ids);

    List<BenefitValueDO> getBenefitValueListByBenefitId(Long benefitId);
} 