package cn.tianxing.cloud.module.member.controller.app.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * App会员卡权益 DTO
 */
@Schema(description = "App会员卡权益查询 DTO")
@Data
public class AppCardBenefitDTO {

    @Schema(description = "类型：0-可绑定卡，1-可升级卡", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "类型不能为空")
    private Integer type;

    @Schema(description = "会员卡ID，当type=1时必传", example = "1024")
    private Long cardId;
}
