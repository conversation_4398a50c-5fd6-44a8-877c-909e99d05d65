package cn.tianxing.cloud.module.member.job.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.tianxing.cloud.framework.common.enums.MemberCardOpeningEnum;
import cn.tianxing.cloud.framework.common.pojo.CommonResult;
import cn.tianxing.cloud.framework.common.pojo.PageParam;
import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.framework.tenant.core.job.TenantJob;
import cn.tianxing.cloud.module.crm.api.caseentrust.CaseEntrustApi;
import cn.tianxing.cloud.module.member.dal.dataobject.card.MemberCardBindDO;
import cn.tianxing.cloud.module.member.dal.dataobject.card.MemberCardDO;
import cn.tianxing.cloud.module.member.dal.dataobject.card.MemberCardUserActivationDO;
import cn.tianxing.cloud.module.member.dal.mysql.card.MemberCardBindMapper;
import cn.tianxing.cloud.module.member.dal.mysql.card.MemberCardUserActivationMapper;
import cn.tianxing.cloud.module.member.service.card.MemberCardActivationService;
import cn.tianxing.cloud.module.member.service.card.MemberCardService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class MemberJob {

    @Resource
    private MemberCardService memberCardService;

    @Resource
    private MemberCardBindMapper memberCardBindMapper;

    @Resource
    private MemberCardUserActivationMapper memberCardUserActivationMapper;

    @Resource
    private MemberCardActivationService memberCardActivationService;

    @Resource
    private CaseEntrustApi caseEntrustApi;

    /**
     * 会员达标任务
     *
     * @param params
     */
    @XxlJob("memberJob")
    @TenantJob
    public void execute(String params) {

        // 1.设置分页参数
        PageParam pageParam = new PageParam();
        // 获取参数
        Integer pageNo = 1;
        Integer pageSize = 1000;
        // 解析参数
        if (StrUtil.isNotBlank(params)) {
            JSONObject json = JSON.parseObject(params);
            if (Objects.nonNull(json.getInteger("pageNo"))) {
                pageNo = json.getInteger("pageNo");
            }
            if (Objects.nonNull(json.getInteger("pageSize"))) {
                pageSize = json.getInteger("pageSize");
            }
        }
        // 设置参数
        pageParam.setPageNo(pageNo);
        pageParam.setPageSize(pageSize);
        log.info("页码:" + pageNo + " 每页条数:" + pageSize);

        // 2.获取会员卡开通条件
        List<MemberCardDO> memberCardList = memberCardService.getMemberCardList();
        if (CollUtil.isEmpty(memberCardList)) {
            return;
        }
        List<Long> memberCardIdList = memberCardList.stream().map(MemberCardDO::getId).collect(Collectors.toList());
        Map<Long, Set<String>> memberCardActivationMap = memberCardActivationService.getMemberCardActivationMapList(memberCardIdList);

        // 3.组装会员卡完成数据
        Map<String, List<Long>> finishMap = new HashMap<>();
        List<MemberCardUserActivationDO> saveList = new ArrayList<>();
        while (true) {
            // 会员卡分页查询
            PageResult<MemberCardBindDO> memberCardBindPageResult = memberCardBindMapper.selectPage(pageParam);
            List<MemberCardBindDO> memberCardBindList = memberCardBindPageResult.getList();
            if (CollUtil.isEmpty(memberCardBindList)) {
                log.info("结束循环");
                break;
            } else {
                for (MemberCardBindDO memberCardBindDO : memberCardBindList) {
                    Set<String> userCardOpenConditionSet = gatherMemberCardUserFinishInfo(memberCardBindDO.getId(), memberCardBindDO.getMemberUserId(), finishMap);
                    Set<String> cardOpenConditionSet = memberCardActivationMap.get(memberCardBindDO.getCardId());
                    gatherMemberCardUserSaveInfo(memberCardBindDO.getId(), userCardOpenConditionSet, cardOpenConditionSet, saveList);
                }
                pageParam.setPageNo(pageParam.getPageNo() + 1);
                log.info("循环页码:" + pageParam.getPageNo());
            }
        }

        // 4.更新会员卡完成情况
        for (Map.Entry<String, List<Long>> entry : finishMap.entrySet()) {
            String key = entry.getKey();
            List<Long> value = entry.getValue();
            if (CollUtil.isNotEmpty(value)) {
                memberCardUserActivationMapper.updateStatus(value, 1, key);
            }
        }

        // 5.批量保存会员卡完成情况
        if (CollUtil.isNotEmpty(saveList)) {
            memberCardUserActivationMapper.insertBatch(saveList);
        }
    }

    private void gatherMemberCardUserSaveInfo(Long memberCardBindId, Set<String> openConditionSet, Set<String> cardOpenConditionSet, List<MemberCardUserActivationDO> saveList) {
        // 检查是否包含所有元素
        if (!openConditionSet.containsAll(cardOpenConditionSet)) {
            // 计算差集：cardOpenConditionSet 中有但 openConditionSet 中没有的元素
            Set<String> difference = new HashSet<>(cardOpenConditionSet);
            difference.removeAll(openConditionSet);
            for (String openCondition : difference) {
                MemberCardUserActivationDO memberCardUserActivationDO = new MemberCardUserActivationDO();
                memberCardUserActivationDO.setCardBindId(memberCardBindId);
                memberCardUserActivationDO.setOpenCondition(openCondition);
                memberCardUserActivationDO.setFinishStatus(0);
                LocalDateTime now = LocalDateTime.now();
                memberCardUserActivationDO.setCreateTime(now);
                memberCardUserActivationDO.setUpdateTime(now);
                saveList.add(memberCardUserActivationDO);
            }
        }
    }

    /**
     * 收集用户会员卡开通条件已完成信息
     *
     * @param memberCardBindId 会员卡绑定编号
     * @param memberUserId     会员用户编号
     * @param finishMap        完成开通条件Map
     * @return 返回已完成信息
     */
    private Set<String> gatherMemberCardUserFinishInfo(Long memberCardBindId, Long memberUserId, Map<String, List<Long>> finishMap) {

        // 1.根据会员卡绑定编号查询会员卡开通条件
        List<MemberCardUserActivationDO> memberCardActivationDOList = memberCardUserActivationMapper.selectListByCardBindId(memberCardBindId);
        if (CollUtil.isEmpty(memberCardActivationDOList)) {
            return Set.of();
        }

        // 2.验证用户会员卡开通条件
        Set<String> openConditionSet = new HashSet<>();
        for (MemberCardUserActivationDO memberCardActivationDO : memberCardActivationDOList) {
            Long id = memberCardActivationDO.getId();
            String openCondition = memberCardActivationDO.getOpenCondition();
            Integer finishStatus = memberCardActivationDO.getFinishStatus();
            if (Objects.nonNull(finishStatus) && finishStatus == 0) {
                if (CollUtil.isEmpty(finishMap.get(openCondition))) {
                    // 第一次进来初始化
                    finishMap.put(openCondition, new ArrayList<>());
                }
                verificationActivationConditions(id, openCondition, finishMap, finishMap.get(openCondition), memberUserId);
            }
            openConditionSet.add(openCondition);
        }

        // 3.返回用户会员开通条件
        return openConditionSet;
    }

    /**
     * 验证会员卡开通条件
     *
     * @param id                  开通条件编号
     * @param activationCondition 开通条件
     * @param finishMap           完成开通条件Map
     * @param finishList          完成开通条件List
     * @param memberUserId        会员用户编号
     */
    private void verificationActivationConditions(Long id, String activationCondition, Map<String, List<Long>> finishMap, List<Long> finishList, Long memberUserId) {

        // 验证开通条件
        if (activationCondition.equals(MemberCardOpeningEnum.USER_REGISTERED.getType())) {
            // 用户注册
            finishList.add(id);
        } else if (activationCondition.equals(MemberCardOpeningEnum.APPLICATION_SUBMITTED.getType())) {
            // 申请开通
            finishList.add(id);
        } else if (activationCondition.equals(MemberCardOpeningEnum.CREDITOR_ROLE.getType())) {
            // 债权人
            CommonResult<Boolean> creditAndDebtAttestation = caseEntrustApi.getCreditAndDebtAttestation(memberUserId, MemberCardOpeningEnum.CREDITOR_ROLE.getType());
            if (creditAndDebtAttestation.getData()) {
                finishList.add(id);
            }
        } else if (activationCondition.equals(MemberCardOpeningEnum.DEBTOR_ROLE.getType())) {
            // 债务人
            CommonResult<Boolean> creditAndDebtAttestation = caseEntrustApi.getCreditAndDebtAttestation(memberUserId, MemberCardOpeningEnum.DEBTOR_ROLE.getType());
            if (creditAndDebtAttestation.getData()) {
                finishList.add(id);
            }
        } else if (activationCondition.equals(MemberCardOpeningEnum.SILVER_CREDIT_OVER_30K.getType())) {
            // 债权额在3万以上
            CommonResult<Boolean> creditAndDebtAttestation = caseEntrustApi.getCreditAndDebtAttestation(memberUserId, MemberCardOpeningEnum.SILVER_CREDIT_OVER_30K.getType());
            if (creditAndDebtAttestation.getData()) {
                finishList.add(id);
            }
        } else {
            return;
        }

        // 添加完成开通条件
        finishMap.put(activationCondition, finishList);
    }


}
