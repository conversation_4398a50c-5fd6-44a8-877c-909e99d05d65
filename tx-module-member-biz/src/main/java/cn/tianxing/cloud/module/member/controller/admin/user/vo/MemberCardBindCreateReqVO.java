package cn.tianxing.cloud.module.member.controller.admin.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;
import java.time.LocalDate;

import static cn.tianxing.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * 会员卡绑定 Request VO
 */
@Schema(description = "管理后台 - 会员卡绑定创建 Request VO")
@Data
public class MemberCardBindCreateReqVO {

    @Schema(description = "会员用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "会员用户编号不能为空")
    private Long memberUserId;

    @Schema(description = "会员卡编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2048")
    @NotNull(message = "会员卡编号不能为空")
    private Long cardId;

    @Schema(description = "会员费用", example = "100.00")
    @Positive(message = "会员费用必须大于0")
    private BigDecimal fee;

    @Schema(description = "关联流水号", example = "FLOW123456")
    private String flowNumber;

    @Schema(description = "生效日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "生效日期不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate effectiveDate;

    @Schema(description = "到期日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate expireDate;

    @Schema(description = "备注", example = "VIP会员")
    private String remark;
} 