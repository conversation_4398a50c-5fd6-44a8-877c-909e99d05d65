package cn.tianxing.cloud.module.member.dal.mysql.card;

import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.tianxing.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tianxing.cloud.module.member.controller.admin.benefit.vo.BenefitQueryReqVO;
import cn.tianxing.cloud.module.member.dal.dataobject.card.BenefitDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 权益配置 Mapper
 */
@Mapper
public interface BenefitMapper extends BaseMapperX<BenefitDO> {
    
    /**
     * 根据条件查询权益配置列表
     */
    default PageResult<BenefitDO> selectPage(BenefitQueryReqVO queryVO) {
        return selectPage(queryVO, new LambdaQueryWrapperX<BenefitDO>()
                .eqIfPresent(BenefitDO::getBenefitType, queryVO.getBenefitType())
                .likeIfPresent(BenefitDO::getBenefitName, queryVO.getBenefitName())
                .eqIfPresent(BenefitDO::getCanWithdraw, queryVO.getCanWithdraw())
                .likeIfPresent(BenefitDO::getRule, queryVO.getRule())
                .eqIfPresent(BenefitDO::getStatus, queryVO.getStatus())
        );
    }
} 