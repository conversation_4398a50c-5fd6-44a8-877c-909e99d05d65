package cn.tianxing.cloud.module.member.controller.app.card.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "用户 App - 会员卡开通详情 VO")
@Data
public class AppMemberCardUserActivationDetailsVo {

    @Schema(description = "会员卡开通方式详情")
    private List<AppMemberCardUserActivationModeVo> activationModeList;

    @Schema(description = "会员卡开通费用")
    private BigDecimal memberFee;

}
