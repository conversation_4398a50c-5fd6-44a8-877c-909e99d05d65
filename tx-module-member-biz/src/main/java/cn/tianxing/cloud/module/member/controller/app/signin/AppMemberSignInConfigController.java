//package cn.iocoder.yudao.module.member.controller.app.signin;
//
//import cn.tianxing.cloud.framework.common.enums.CommonStatusEnum;
//import cn.tianxing.cloud.framework.common.pojo.CommonResult;
//import config.vo.signin.app.controller.cn.tianxing.cloud.module.member.AppMemberSignInConfigRespVO;
//import signin.convert.cn.tianxing.cloud.module.member.MemberSignInConfigConvert;
//import signin.dataobject.dal.cn.tianxing.cloud.module.member.MemberSignInConfigDO;
//import signin.service.cn.tianxing.cloud.module.member.MemberSignInConfigService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import jakarta.annotation.security.PermitAll;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import jakarta.annotation.Resource;
//import java.util.List;
//
//import static cn.tianxing.cloud.framework.common.pojo.CommonResult.success;
//
//@Tag(name = "用户 App - 签到规则")
//@RestController
//@RequestMapping("/member/sign-in/config")
//@Validated
//public class AppMemberSignInConfigController {
//
//    @Resource
//    private MemberSignInConfigService signInConfigService;
//
//    @GetMapping("/list")
//    @Operation(summary = "获得签到规则列表")
//    @PermitAll
//    public CommonResult<List<AppMemberSignInConfigRespVO>> getSignInConfigList() {
//        List<MemberSignInConfigDO> pageResult = signInConfigService.getSignInConfigList(CommonStatusEnum.ENABLE.getStatus());
//        return success(MemberSignInConfigConvert.INSTANCE.convertList02(pageResult));
//    }
//
//}
