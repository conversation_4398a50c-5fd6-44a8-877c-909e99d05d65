package cn.tianxing.cloud.module.member.service.card.impl;

import cn.tianxing.cloud.framework.common.exception.util.ServiceExceptionUtil;
import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.framework.common.util.object.BeanUtils;
import cn.tianxing.cloud.module.member.controller.admin.card.vo.*;
import cn.tianxing.cloud.module.member.controller.admin.user.vo.CardBenefitRespVO;
import cn.tianxing.cloud.module.member.convert.card.MemberCardConvert;
import cn.tianxing.cloud.module.member.dal.dataobject.card.CardBenefitDO;
import cn.tianxing.cloud.module.member.dal.dataobject.card.MemberCardActivationDO;
import cn.tianxing.cloud.module.member.dal.dataobject.card.MemberCardDO;
import cn.tianxing.cloud.module.member.dal.mysql.card.MemberCardMapper;
import cn.tianxing.cloud.module.member.enums.ErrorCodeConstants;
import cn.tianxing.cloud.module.member.service.card.CardBenefitService;
import cn.tianxing.cloud.module.member.service.card.MemberCardActivationService;
import cn.tianxing.cloud.module.member.service.card.MemberCardService;
import cn.tianxing.cloud.module.system.api.user.UserIdApi;
import cn.tianxing.cloud.module.system.api.user.vo.UserVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员卡 Service 实现类
 */
@Service
@Validated
public class MemberCardServiceImpl extends ServiceImpl<MemberCardMapper, MemberCardDO> implements MemberCardService  {

    @Resource
    private MemberCardMapper memberCardMapper;
    
    @Resource
    private UserIdApi userIdApi;
    
    @Resource
    private CardBenefitService cardBenefitService;
    
    @Resource
    private MemberCardActivationService memberCardActivationService;

    @Override
    public MemberCardDO getMemberCard(Long id) {
        return memberCardMapper.selectById(id);
    }

    @Override
    public List<MemberCardDO> getMemberCardList() {
        return memberCardMapper.selectList();
    }

    @Override
    public List<MemberCardDO> getEnableMemberCardList() {
        return memberCardMapper.selectListByEnable();
    }
    
    @Override
    public PageResult<MemberCardPageRespVO> getMemberCardPage(MemberCardPageReqVO pageReqVO) {
        // 获取会员卡分页数据
        PageResult<MemberCardDO> pageResult = memberCardMapper.selectPage(pageReqVO);
        
        // 转换为响应VO
        PageResult<MemberCardPageRespVO> respVOPageResult = MemberCardConvert.INSTANCE.convertPage(pageResult);
        
        // 获取所有更新人ID
        List<Long> updaterIds = new ArrayList<>();
        for (MemberCardDO cardDO : pageResult.getList()) {
            if (cardDO.getUpdater() != null && !cardDO.getUpdater().isEmpty()) {
                try {
                    updaterIds.add(Long.valueOf(cardDO.getUpdater()));
                } catch (NumberFormatException e) {
                    // 忽略非数字格式的updater
                    continue;
                }
            }
        }
        
        // 如果有更新人ID，则查询用户信息
        if (!updaterIds.isEmpty()) {
            // 调用UserIdApi获取用户信息
            Map<Long, UserVO> userMap = userIdApi.getUserMapByIds(updaterIds);
            
            // 设置更新人名称
            respVOPageResult.getList().forEach(respVO -> {
                MemberCardDO cardDO = pageResult.getList().stream()
                        .filter(item -> item.getId().equals(respVO.getId()))
                        .findFirst()
                        .orElse(null);
                
                if (cardDO != null && cardDO.getUpdater() != null && !cardDO.getUpdater().isEmpty()) {
                    try {
                        Long updaterId = Long.valueOf(cardDO.getUpdater());
                        UserVO userVO = userMap.get(updaterId);
                        if (userVO != null) {
                            respVO.setUpdaterName(userVO.getConcatName());
                        }
                    } catch (NumberFormatException e) {
                        // 忽略非数字格式的updater
                    }
                }
            });
        }
        
        // 处理可升级别和可降级别
        // 1. 收集所有的卡编码
        Map<String, String> cardCodeNameMap = new HashMap<>();
        // 所有会员卡数据
        List<MemberCardDO> allCards = memberCardMapper.selectList();
        // 构建卡编码到卡名称的映射
        allCards.forEach(card -> {
            if (card.getCode() != null) {
                cardCodeNameMap.put(card.getCode(), card.getCardName());
            }
        });
        
        // 2. 处理每个会员卡的可升级别和可降级别
        respVOPageResult.getList().forEach(respVO -> {
            respVO.setUpgradableLevelName(getLevelNames(respVO.getUpgradableLevel(), cardCodeNameMap));
            respVO.setDowngradableLevelName(getLevelNames(respVO.getDowngradableLevel(), cardCodeNameMap));
        });
        
        return respVOPageResult;
    }
    
    @Override
    public void updateMemberCardStatus(MemberCardUpdateStatusReqVO updateReqVO) {
        // 校验会员卡是否存在
        MemberCardDO memberCard = memberCardMapper.selectById(updateReqVO.getId());
        if (memberCard == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MEMBER_CARD_NOT_EXISTS);
        }
        // 如果状态相同，直接返回
        if (memberCard.getStatus().equals(updateReqVO.getStatus())) {
            return;
        }
        // 更新状态
        memberCardMapper.updateById(new MemberCardDO()
                .setId(updateReqVO.getId())
                .setStatus(updateReqVO.getStatus()));
    }
    
    @Override
    public List<MemberCardSimpleRespVO> getMemberCardListExclude(Long cardId) {
        // 查询除指定卡id外的会员卡列表
        List<MemberCardDO> cardList = memberCardMapper.selectListExclude(cardId);
        // 转换为简单响应VO
        return MemberCardConvert.INSTANCE.convertSimpleList(cardList);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveMemberCard(MemberCardSaveVO saveVO) {
        // 1. 保存会员卡基本信息
        MemberCardDO memberCardDO = BeanUtils.toBean(saveVO, MemberCardDO.class);
        //生成卡编码
        if (memberCardDO.getId() == null) {
            memberCardDO.setCode(generateCardCode());
        }
        memberCardMapper.insertOrUpdate(memberCardDO);
        Long cardId = memberCardDO.getId();

        // 2. 处理会员卡权益关联
        // 先删除原有关联
        cardBenefitService.remove(new LambdaQueryWrapper<CardBenefitDO>().eq(CardBenefitDO::getCardId, cardId));
        // 添加新的关联
        if (saveVO.getBenefitIds() != null && !saveVO.getBenefitIds().isEmpty()) {
            List<CardBenefitDO> cardBenefitDOList = new ArrayList<>();
            saveVO.getBenefitIds().forEach(benefitId -> {
                CardBenefitDO cardBenefit = new CardBenefitDO()
                        .setCardId(cardId)
                        .setBenefitId(benefitId);
                cardBenefitDOList.add(cardBenefit);
            });
            cardBenefitService.saveBatch(cardBenefitDOList);
        }

        // 先删除原有开通条件、结束条件
        LambdaQueryWrapper<MemberCardActivationDO> openConditionQuery = new LambdaQueryWrapper<MemberCardActivationDO>()
                .eq(MemberCardActivationDO::getCardId, cardId);
        // 3. 处理会员卡开通条件和结束条件
        memberCardActivationService.remove(openConditionQuery);
        List<MemberCardActivationDO> conditionList = new ArrayList<>();
        
        // 添加新的开通条件（需要将每个activationGroup内的条件拆分为多条记录）
        if (saveVO.getMemberCardOpenList() != null && !saveVO.getMemberCardOpenList().isEmpty()) {
            saveVO.getMemberCardOpenList().forEach(openDo -> {
                String openCondition = openDo.getOpenCondition();
                Integer activationGroup = openDo.getActivationGroup();

                if (openCondition != null && !openCondition.isEmpty()) {
                    // 按逗号分隔条件
                    String[] conditions = openCondition.split(",");
                    for (String condition : conditions) {
                        if (condition != null && !condition.trim().isEmpty()) {
                            MemberCardActivationDO newCondition = new MemberCardActivationDO();
                            newCondition.setCardId(cardId);
                            newCondition.setOpenCondition(condition.trim());
                            newCondition.setActivationGroup(activationGroup);
                            newCondition.setType(0);
                            conditionList.add(newCondition);
                        }
                    }
                }
            });
        }
        
        // 添加新的结束条件
        if (saveVO.getMemberCardEndList() != null && !saveVO.getMemberCardEndList().isEmpty()) {
            saveVO.getMemberCardEndList().forEach(endDo -> {
                String endCondition = endDo.getEndCondition();
                Integer activationGroup = endDo.getActivationGroup();
                
                if (endCondition != null && !endCondition.isEmpty()) {
                    // 按逗号分隔条件
                    String[] conditions = endCondition.split(",");
                    for (String condition : conditions) {
                        if (condition != null && !condition.trim().isEmpty()) {
                            MemberCardActivationDO newCondition = new MemberCardActivationDO();
                            newCondition.setCardId(cardId);
                            newCondition.setEndCondition(condition.trim());
                            newCondition.setActivationGroup(activationGroup);
                            newCondition.setType(1);
                            conditionList.add(newCondition);
                        }
                    }
                }
            });
        }
        
        memberCardActivationService.saveBatch(conditionList);
        return cardId;
    }

    @Override
    public MemberCardDetailVO getMemberCardDetail(Long id) {
        // 1. 获取会员卡信息
        MemberCardDO memberCard = memberCardMapper.selectById(id);
        if (memberCard == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MEMBER_CARD_NOT_EXISTS);
        }
        MemberCardDetailVO detailVO = BeanUtils.toBean(memberCard, MemberCardDetailVO.class);

        // 2. 处理可升级、可降级别名称
        Map<String, String> cardCodeNameMap = memberCardMapper.selectList().stream()
                .collect(Collectors.toMap(MemberCardDO::getCode, MemberCardDO::getCardName, (v1, v2) -> v1));
        detailVO.setUpgradableLevelName(getLevelNames(detailVO.getUpgradableLevel(), cardCodeNameMap));
        detailVO.setDowngradableLevelName(getLevelNames(detailVO.getDowngradableLevel(), cardCodeNameMap));

        // 3. 获取开通和结束条件
        List<MemberCardActivationDO> activations = memberCardActivationService.list(
                new LambdaQueryWrapper<MemberCardActivationDO>().eq(MemberCardActivationDO::getCardId, id));
        
        // 按类型和分组进行分类合并
        // 处理开通条件 (type=0)
        Map<Integer, List<MemberCardActivationDO>> openGroupMap = activations.stream()
                .filter(a -> Integer.valueOf(0).equals(a.getType()))
                .collect(Collectors.groupingBy(MemberCardActivationDO::getActivationGroup));
        
        List<MemberCardActivationDO> mergedOpenList = new ArrayList<>();
        openGroupMap.forEach((group, items) -> {
            if (!items.isEmpty()) {
                MemberCardActivationDO merged = new MemberCardActivationDO();
                merged.setCardId(id);
                merged.setActivationGroup(group);
                merged.setType(0);
                
                // 合并同一组的开通条件
                String openCondition = items.stream()
                        .map(MemberCardActivationDO::getOpenCondition)
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining(","));
                merged.setOpenCondition(openCondition);
                
                mergedOpenList.add(merged);
            }
        });
        
        // 处理结束条件 (type=1)
        Map<Integer, List<MemberCardActivationDO>> endGroupMap = activations.stream()
                .filter(a -> Integer.valueOf(1).equals(a.getType()))
                .collect(Collectors.groupingBy(MemberCardActivationDO::getActivationGroup));
        
        List<MemberCardActivationDO> mergedEndList = new ArrayList<>();
        endGroupMap.forEach((group, items) -> {
            if (!items.isEmpty()) {
                MemberCardActivationDO merged = new MemberCardActivationDO();
                merged.setCardId(id);
                merged.setActivationGroup(group);
                merged.setType(1);
                
                // 合并同一组的结束条件
                String endCondition = items.stream()
                        .map(MemberCardActivationDO::getEndCondition)
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining(","));
                merged.setEndCondition(endCondition);
                
                mergedEndList.add(merged);
            }
        });
        
        detailVO.setMemberCardOpenList(mergedOpenList);
        detailVO.setMemberCardEndList(mergedEndList);

        List<CardBenefitRespVO> cardBenefits = cardBenefitService.getCardBenefitsByCardId(id);
        detailVO.setCardBenefits(cardBenefits);
        return detailVO;
    }

    private String getLevelNames(String levelCodes, Map<String, String> cardCodeNameMap) {
        if (StringUtils.isBlank(levelCodes)) {
            return null;
        }
        List<String> names = Arrays.stream(levelCodes.split(","))
                .map(String::trim)
                .map(cardCodeNameMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (names.isEmpty()) {
            return null;
        }
        return String.join("，", names);
    }

    private String generateCardCode() {
        // 1. 获取当前最大卡编码
        MemberCardDO maxCodeCard = memberCardMapper.selectMaxCode();
        // 2. 生成新编码
        String prefix = "HY";
        int newNum = 1;
        if (maxCodeCard != null && maxCodeCard.getCode() != null && maxCodeCard.getCode().startsWith(prefix)) {
            String numStr = maxCodeCard.getCode().substring(prefix.length());
            newNum = Integer.parseInt(numStr) + 1;
        }
        // 3. 格式化新编码
        return prefix + String.format("%03d", newNum);
    }
} 