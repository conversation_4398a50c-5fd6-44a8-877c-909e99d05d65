package cn.tianxing.cloud.module.member.service.card;

import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.module.member.controller.admin.benefit.vo.BenefitQueryReqVO;
import cn.tianxing.cloud.module.member.controller.admin.benefit.vo.BenefitReqVO;
import cn.tianxing.cloud.module.member.controller.admin.benefit.vo.BenefitRespVO;
import cn.tianxing.cloud.module.member.dal.dataobject.card.BenefitDO;

import java.util.List;
import java.util.Collection;

/**
 * 权益配置 Service 接口
 */
public interface BenefitService {

    /**
     * 创建权益配置
     *
     * @param reqVO 创建信息
     * @return 权益配置编号
     */
    Long createBenefit(BenefitReqVO reqVO);

    /**
     * 更新权益配置
     *
     * @param reqVO 更新信息
     */
    void updateBenefit(BenefitReqVO reqVO);

    /**
     * 删除权益配置
     *
     * @param id 权益配置编号
     */
    void deleteBenefit(Long id);

    /**
     * 获得权益配置
     *
     * @param id 权益配置编号
     * @return 权益配置
     */
    BenefitRespVO getBenefit(Long id);

    /**
     * 获得权益配置列表
     *
     * @param ids 权益配置编号列表
     * @return 权益配置列表
     */
    List<BenefitDO> getBenefitList(Collection<Long> ids);

    /**
     * 获得所有权益配置列表
     *
     * @return 权益配置列表
     */
    List<BenefitDO> getBenefitList();
    
    /**
     * 获得有效的权益配置列表
     *
     * @return 权益配置列表
     */
    List<BenefitDO> getEnabledBenefitList();

    /**
     * 根据条件查询权益配置列表
     */
    PageResult<BenefitRespVO> getBenefitListByCondition(BenefitQueryReqVO queryVO);
} 