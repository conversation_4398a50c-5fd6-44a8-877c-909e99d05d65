package cn.tianxing.cloud.module.member.controller.admin.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import jakarta.validation.constraints.NotNull;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static cn.tianxing.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 会员卡绑定创建 Request DTO")
@Data
public class MemberCardBindCreateReqDTO {

    @Schema(description = "会员id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "会员id不能为空")
    private Long memberUserId;

    @Schema(description = "会员卡id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "会员卡id不能为空")
    private Long cardId;

    @Schema(description = "会员卡code", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "会员卡code不能为空")
    private String cardCode;

    @Schema(description = "会员卡名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "会员卡名称不能为空")
    private String cardName;

    @Schema(description = "关联流水号")
    private String flowNumber;

    @Schema(description = "费用", example = "100.00")
    private Double fee;

    @Schema(description = "支付方式", example = "1")
    private Integer payMethod;

    @Schema(description = "支付状态", example = "1")
    private Integer payStatus;

    @Schema(description = "有效日期", example = "2023-01-01")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate effectiveDate;

    @Schema(description = "到期日期", example = "2024-01-01")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate expireDate;

    @Schema(description = "状态", example = "1")
    private Integer status;

    @Schema(description = "备注", example = "开通年卡")
    private String remark;
} 