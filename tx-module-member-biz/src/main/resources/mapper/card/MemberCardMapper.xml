<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.tianxing.cloud.module.member.dal.mysql.card.MemberCardMapper">

    <resultMap id="BaseResultMap" type="cn.tianxing.cloud.module.member.dal.dataobject.card.MemberCardDO">
        <id column="id" property="id"/>
        <result column="card_name" property="cardName"/>
        <result column="card_image" property="cardImage"/>
        <result column="card_slogan_image" property="cardSloganImage"/>
        <result column="small_icon" property="smallIcon"/>
        <result column="period" property="period"/>
        <result column="period_type" property="periodType"/>
        <result column="member_identity" property="memberIdentity"/>
        <result column="member_fee" property="memberFee"/>
        <result column="activation_condition_description" property="activationConditionDescription"/>
        <result column="end_condition_description" property="endConditionDescription"/>
        <result column="member_count" property="memberCount"/>
        <result column="upgradable_level" property="upgradableLevel"/>
        <result column="downgradable_level" property="downgradableLevel"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="code" property="code"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="updater" property="updater"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

</mapper> 