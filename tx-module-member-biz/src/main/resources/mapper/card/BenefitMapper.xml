<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.tianxing.cloud.module.member.dal.mysql.card.BenefitMapper">

    <resultMap id="BaseResultMap" type="cn.tianxing.cloud.module.member.dal.dataobject.card.BenefitDO">
        <id column="id" property="id"/>
        <result column="benefit_type" property="benefitType"/>
        <result column="benefit_name" property="benefitName"/>
        <result column="benefit_icon" property="benefitIcon"/>
        <result column="rule" property="rule"/>
        <result column="benefit_value" property="benefitValue"/>
        <result column="can_withdraw" property="canWithdraw"/>
        <result column="benefit_description" property="benefitDescription"/>
        <result column="status" property="status"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="updater" property="updater"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

</mapper> 