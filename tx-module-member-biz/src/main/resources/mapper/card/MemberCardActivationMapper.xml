<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.tianxing.cloud.module.member.dal.mysql.card.MemberCardActivationMapper">

    <resultMap id="BaseResultMap" type="cn.tianxing.cloud.module.member.dal.dataobject.card.MemberCardActivationDO">
        <id column="id" property="id" />
        <result column="card_id" property="cardId" />
        <result column="open_condition" property="openCondition" />
        <result column="group" property="group" />
        <result column="end_condition" property="endCondition" />
        <result column="status" property="status" />
        <result column="type" property="type" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 根据会员卡ID、分组、查询开通条件列表 -->
    <select id="selectListMemberCardActivation" resultType="cn.tianxing.cloud.module.member.controller.app.card.vo.AppMemberCardUserActivationFinishVo">
        SELECT
            0 AS finishStatus,
            open_condition,
            '' AS description
        FROM
            member_card_activation
        WHERE
            deleted = 0
        AND type = 0
        <if test="cardId != null">
            AND card_id = #{cardId}
        </if>
        <if test="activationGroup != null">
            AND activation_group = #{activationGroup}
        </if>
    </select>

    <!-- 根据会员卡ID查询开通条件列表 -->
    <select id="selectListOpenCondition" resultType="java.lang.String" parameterType="java.lang.Long">
        SELECT
            open_condition
        FROM
            member_card_activation
        WHERE
            deleted = 0
        AND type = 0
        AND open_condition IS NOT NULL
        <if test="cardId != null">
            AND card_id = #{cardId}
        </if>
        GROUP BY open_condition
    </select>

    <!-- 查询开通方式分组 -->
    <select id="selectListActivationGroup" resultType="java.lang.Integer">
        SELECT activation_group FROM member_card_activation WHERE deleted = 0 AND type = 0 GROUP BY activation_group
    </select>


</mapper>