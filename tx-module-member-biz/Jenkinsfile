pipeline {
    agent any
    parameters {
        choice(name: 'ENV', choices: ['dev', 'prod'], description: '选择部署环境')
    }
    tools {
        maven 'M3'
    }
    stages {
        stage('Clean') {
            steps {
                sh 'mvn clean install -U -DskipTests -Dmaven.test.skip=true -Dmaven.test.failure.ignore=true'
            }
        }
        stage('Build') {
            steps {
                sh 'mvn package -DskipTests -Dmaven.test.skip=true -Dmaven.test.failure.ignore=true'
            }
        }
        stage('Deploy') {
            steps {
                script {
                    def createTransfer = { String configName, String remoteDir ->
                        sshPublisherDesc(
                            configName: configName,
                            transfers: [
                                sshTransfer(
                                    sourceFiles: 'tx-module-member-biz/target/tx-module-member-biz.jar',
                                    removePrefix: 'tx-module-member-biz/target',
                                    remoteDirectory: '/opt',
                                    execCommand: '''
                                        systemctl restart tx-module-member.service
                                    '''
                                )
                            ]
                        )
                    }

                    if (params.ENV == 'dev') {
                        sshPublisher publishers: [createTransfer('dev-member', '/opt')]
                    } else {
                        def prodNodes = [
                            [configName: 'prod-member1', remoteDir: '/opt'],
                            [configName: 'prod-member2', remoteDir: '/opt']
                            // 根据需要添加更多节点
                        ]
                        sshPublisher publishers: prodNodes.collect { createTransfer(it.configName, it.remoteDir) }
                    }
                }
            }
        }
    }
}