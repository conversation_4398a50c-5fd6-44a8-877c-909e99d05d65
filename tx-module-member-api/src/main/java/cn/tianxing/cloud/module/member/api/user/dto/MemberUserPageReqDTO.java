package cn.tianxing.cloud.module.member.api.user.dto;

import cn.tianxing.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "RPC 服务 - 会员用户分页查询 Request DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberUserPageReqDTO extends PageParam {

    @Schema(description = "用户昵称", example = "张三")
    private String nickname;

    @Schema(description = "手机号码", example = "15601691300")
    private String mobile;

    @Schema(description = "状态", example = "0")
    private Integer status;
} 