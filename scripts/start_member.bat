@echo off
chcp 65001 >nul
echo Starting Member service...
echo 正在启动member服务...

cd /d "%~dp0.."
set JAVA_OPTS=-Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui -Xms512m -Xmx1024m -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 -Dlog4j.charset=UTF-8 -Dlog4j2.charset=UTF-8

@REM echo Compiling Member module...
@REM echo 正在重新编译member模块...
@REM call mvn -f tx-module-member/pom.xml clean install -DskipTests
@REM if %errorlevel% neq 0 (
@REM     echo Member module compilation failed, please check error messages
@REM     echo member模块编译失败，请检查错误信息
@REM     pause
@REM     exit /b 1
@REM )
@REM echo Member module compilation completed
@REM echo member模块编译完成

echo Starting Member service...
echo 正在启动member服务...
call mvn -f tx-module-member/tx-module-member-biz/pom.xml spring-boot:run -Dspring-boot.run.jvmArguments="%JAVA_OPTS%"

echo Member service started
echo member服务已启动 