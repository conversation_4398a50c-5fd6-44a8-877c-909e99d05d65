@echo off
chcp 65001 >nul
echo JVM参数位置对比示例
echo ========================

cd /d "%~dp0.."
set JAVA_OPTS=-Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui -Xms512m -Xmx1024m -Dfile.encoding=UTF-8

echo.
echo 方式一：spring-boot:run（Maven插件方式）
echo 参数位置：mvn spring-boot:run -Dspring-boot.run.jvmArguments="参数"
echo 示例：
echo call mvn -f tx-module-crm/tx-module-crm-biz/pom.xml spring-boot:run -Dspring-boot.run.jvmArguments="%JAVA_OPTS%"
echo.

echo 方式二：java -jar（直接Java命令方式）
echo 参数位置：java 参数 -jar application.jar
echo 示例：
echo java %JAVA_OPTS% -jar tx-module-crm/tx-module-crm-biz/target/tx-module-crm-biz-*.jar
echo.

echo 关键区别：
echo 1. spring-boot:run：参数放在spring-boot:run后面
echo 2. java -jar：参数放在java命令后面，-jar前面
echo.

pause 