@echo off
chcp 65001 >nul
echo 正在启动所有服务...

cd /d "%~dp0.."

echo 正在重新编译所有模块...
call mvn clean install -DskipTests
if %errorlevel% neq 0 (
    echo 所有模块编译失败，请检查错误信息
    pause
    exit /b 1
)
echo 所有模块编译完成

cd /d "%~dp0"

echo 正在启动各个服务...
start cmd /k call start_system.bat
timeout /t 5
start cmd /k call start_infra.bat
timeout /t 5
start cmd /k call start_gateway.bat
timeout /t 5
start cmd /k call start_member.bat
timeout /t 5
start cmd /k call start_crm.bat
timeout /t 5
start cmd /k call start_report.bat

echo 所有服务启动指令已发送 