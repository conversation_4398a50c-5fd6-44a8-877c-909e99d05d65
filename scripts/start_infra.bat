@echo off
chcp 65001 >nul
echo Starting Infra service...
echo 正在启动infra服务...

cd /d "%~dp0.."
set JAVA_OPTS=-Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui -Xms512m -Xmx1024m -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 -Dlog4j.charset=UTF-8 -Dlog4j2.charset=UTF-8

@REM echo Compiling Infra module...
@REM echo 正在重新编译infra模块...
@REM call mvn -f tx-cloud-base/tx-module-infra/pom.xml clean install -DskipTests
@REM if %errorlevel% neq 0 (
@REM     echo Infra module compilation failed, please check error messages
@REM     echo infra模块编译失败，请检查错误信息
@REM     pause
@REM     exit /b 1
@REM )
@REM echo Infra module compilation completed
@REM echo infra模块编译完成

echo Starting Infra service...
echo 正在启动infra服务...
call mvn -f tx-cloud-base/tx-module-infra/tx-module-infra-biz/pom.xml spring-boot:run -Dspring-boot.run.jvmArguments="%JAVA_OPTS%"

echo Infra service started
echo infra服务已启动 