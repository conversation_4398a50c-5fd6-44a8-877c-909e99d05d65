@echo off
chcp 65001 >nul
echo Starting Gateway service...
echo 正在启动gateway服务...

cd /d "%~dp0.."
set JAVA_OPTS=-Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui -Xms512m -Xmx1024m -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 -Dlog4j.charset=UTF-8 -Dlog4j2.charset=UTF-8

@REM echo Compiling Gateway module...
@REM echo 正在重新编译gateway模块...
@REM call mvn -f tx-cloud-base/tx-gateway/pom.xml clean install -DskipTests
@REM if %errorlevel% neq 0 (
@REM     echo Gateway module compilation failed, please check error messages
@REM     echo gateway模块编译失败，请检查错误信息
@REM     pause
@REM     exit /b 1
@REM )
@REM echo Gateway module compilation completed
@REM echo gateway模块编译完成

echo Starting Gateway service...
echo 正在启动gateway服务...
call mvn -f tx-cloud-base/tx-gateway/pom.xml spring-boot:run -Dspring-boot.run.jvmArguments="%JAVA_OPTS%"

echo Gateway service started
echo gateway服务已启动 