@echo off
chcp 65001 >nul
echo Starting CRM service...
echo 正在启动crm服务...

cd /d "%~dp0.."
set JAVA_OPTS=-Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui -Xms512m -Xmx1024m -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 -Dlog4j.charset=UTF-8 -Dlog4j2.charset=UTF-8

@REM echo Compiling CRM module...
@REM echo 正在重新编译crm模块...
@REM call mvn -f tx-module-crm/pom.xml clean install -DskipTests
@REM if %errorlevel% neq 0 (
@REM     echo CRM module compilation failed, please check error messages
@REM     echo crm模块编译失败，请检查错误信息
@REM     pause
@REM     exit /b 1
@REM )
@REM echo CRM module compilation completed
@REM echo crm模块编译完成

echo Starting CRM service...
echo 正在启动crm服务...
call mvn -f tx-module-crm/tx-module-crm-biz/pom.xml spring-boot:run -Dspring-boot.run.jvmArguments="%JAVA_OPTS%"

echo CRM service started
echo crm服务已启动 