@echo off
chcp 65001 >nul
echo ????report??...

cd /d "%~dp0.."
set JAVA_OPTS=-Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui -Xms512m -Xmx1024m -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 -Dlog4j.charset=UTF-8 -Dlog4j2.charset=UTF-8

echo ??????report??...
call mvn -f tx-module-report/pom.xml clean install -DskipTests
if %errorlevel% neq 0 (
    echo report??????????????
    pause
    exit /b 1
)
echo report??????

echo ????report??...
call mvn -f tx-module-report/tx-module-report-biz/pom.xml spring-boot:run -Dspring-boot.run.jvmArguments="%JAVA_OPTS%"

echo report????? 
