package cn.tianxing.cloud.module.crm.enums.contract;

import cn.hutool.core.util.ObjUtil;
import cn.tianxing.cloud.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * CRM 合同状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CrmContractStatusEnum implements ArrayValuable<String> {

    DRAFT("draft", "草稿"),
    PROCESS("commit", "审批中"),
    APPROVAL("in_approva", "审批中"),
    APPROVE("end_approval", "审批通过"),
    REJECT("back_approval", "审批驳回"),
    INVALID("cancel", "作废"),
    PERFORMING("in_performance", "履约中"),
    END("end", "结束"),
    CHANGE("change", "变更");

    public static final String[] ARRAYS = Arrays.stream(values()).map(CrmContractStatusEnum::getStatus).toArray(String[]::new);

    /**
     * 状态
     */
    private final String status;
    /**
     * 状态名
     */
    private final String name;

    @Override
    public String[] array() {
        return ARRAYS;
    }

    public static boolean isDraft(String status) {
        return ObjUtil.equal(DRAFT.status, status);
    }

    public static boolean isProcess(String status) {
        return ObjUtil.equal(PROCESS.status, status);
    }

    public static boolean isApprove(String status) {
        return ObjUtil.equal(APPROVE.status, status);
    }

    public static boolean isReject(String status) {
        return ObjUtil.equal(REJECT.status, status);
    }

    public static boolean isInvalid(String status) {
        return ObjUtil.equal(INVALID.status, status);
    }

    public static boolean isPerforming(String status) {
        return ObjUtil.equal(PERFORMING.status, status);
    }

    public static boolean isEnd(String status) {
        return ObjUtil.equal(END.status, status);
    }

    public static boolean isChange(String status) {
        return ObjUtil.equal(CHANGE.status, status);
    }
} 