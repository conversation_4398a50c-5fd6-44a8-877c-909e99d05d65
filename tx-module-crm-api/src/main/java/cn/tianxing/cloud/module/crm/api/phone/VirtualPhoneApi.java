package cn.tianxing.cloud.module.crm.api.phone;

import cn.tianxing.cloud.framework.common.pojo.CommonResult;
import cn.tianxing.cloud.module.crm.api.phone.dto.VirtualPhoneRespDTO;
import cn.tianxing.cloud.module.crm.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 虚拟手机号")
public interface VirtualPhoneApi {

    String PREFIX = ApiConstants.PREFIX + "/phone";

    @GetMapping(PREFIX + "/get-virtual-phone")
    @Operation(summary = "获取虚拟手机号")
    @Parameter(name = "realPhone", description = "真实手机号", required = true, example = "13800138000")
    CommonResult<VirtualPhoneRespDTO> getVirtualPhone(@RequestParam("realPhone") String realPhone);
} 