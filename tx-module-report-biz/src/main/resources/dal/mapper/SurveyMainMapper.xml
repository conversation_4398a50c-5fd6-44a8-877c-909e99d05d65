<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.tianxing.cloud.module.report.dal.mysql.survey.SurveyMainMapper">

    <resultMap id="BaseResultMap" type="cn.tianxing.cloud.module.report.dal.dataobject.survey.SurveyMainDO">
            <id property="id" column="id" />
            <result property="type" column="type" />
            <result property="content" column="content" />
            <result property="description" column="description" />
            <result property="dimensionCoefficient" column="dimension_coefficient" />
            <result property="quantifiedLogic" column="quantified_logic" />
            <result property="createdTime" column="created_time" />
            <result property="updatedTime" column="updated_time" />
            <result property="creator" column="creator" />
            <result property="updater" column="updater" />
            <result property="deleted" column="deleted" />
            <result property="tenantId" column="tenant_id" />
    </resultMap>

    <sql id="Base_Column_List">
        id,type,content,description,dimension_coefficient,choice,quantified_logic,
        created_time,updated_time,creator,updater,deleted,
        tenant_id
    </sql>
</mapper>
