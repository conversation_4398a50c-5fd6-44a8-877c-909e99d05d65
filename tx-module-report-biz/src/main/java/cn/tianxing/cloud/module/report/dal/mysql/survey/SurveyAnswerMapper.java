package cn.tianxing.cloud.module.report.dal.mysql.survey;

import cn.tianxing.cloud.module.report.dal.dataobject.survey.SurveyAnswerDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【survey_answer(模板表，包含常用字段)】的数据库操作Mapper
* @createDate 2025-02-26 10:53:28
* @Entity survey.dataobject.dal.cn.tianxing.cloud.module.report.SurveyAnswerDO
*/
@Mapper
public interface SurveyAnswerMapper extends BaseMapper<SurveyAnswerDO> {

}




