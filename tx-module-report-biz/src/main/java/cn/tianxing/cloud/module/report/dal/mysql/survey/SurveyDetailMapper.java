package cn.tianxing.cloud.module.report.dal.mysql.survey;

import cn.tianxing.cloud.module.report.dal.dataobject.survey.SurveyDetailDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【survey_detail(模板表，包含常用字段)】的数据库操作Mapper
* @createDate 2025-02-25 15:23:50
* @Entity survey.dataobject.dal.cn.tianxing.cloud.module.report.SurveyDetailDO
*/
@Mapper
public interface SurveyDetailMapper extends BaseMapper<SurveyDetailDO> {

}




