package cn.tianxing.cloud.module.report.framework.jmreport.config;

import cn.tianxing.cloud.module.system.api.social.SocialUserApi;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * Security 使用到 Feign 的配置项
 *
 * <AUTHOR>
 */
@AutoConfiguration
@EnableFeignClients(clients = {SocialUserApi.class})
public class YudaoReportRpcAutoConfiguration {

}
