package cn.tianxing.cloud.module.report.controller.app.survey.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "APP - 问卷调研结果历史 VO")
@Data
public class SurveyReportHistoryVO {

    @Schema(description = "问卷调研结果id", example = "6")
    private Long id;

    @Schema(description = "还款时间", example = "12")
    private Integer repaymentTime;

    @Schema(description = "欠款金额", example = "100")
    private Double debtAmount;

    @Schema(description = "scvt模型测算结果", example = "SCVT")
    private String scvt;

    @Schema(description = "调研类型", example = "债务还款诊断")
    private String type ="债务还款诊断";

    @Schema(description = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "s所占比例")
    private Double s;

    @Schema(description = "n所占比例")
    private Double n;

    @Schema(description = "c所占比例")
    private Double c;

    @Schema(description = "p所占比例")
    private Double p;

    @Schema(description = "e所占比例")
    private Double e;

    @Schema(description = "v所占比例")
    private Double v;

    @Schema(description = "t所占比例")
    private Double t;

    @Schema(description = "f所占比例")
    private Double f;
} 