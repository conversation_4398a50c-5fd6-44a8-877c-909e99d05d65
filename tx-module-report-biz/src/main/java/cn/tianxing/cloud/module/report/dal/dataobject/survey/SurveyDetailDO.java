package cn.tianxing.cloud.module.report.dal.dataobject.survey;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 模板表，包含常用字段
 * @TableName survey_detail
 */
@TableName(value ="survey_detail")
@Data
public class SurveyDetailDO implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 问题id
     */
    private Long surveyId;

    /**
     * 选项
     */
    private String opt;

    /**
     * 选项加权
     */
    private Double optionWeighting;

    /**
     * scvt模型
     */
    private String scvt;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 删除标志（0：未删除，1：已删除）
     */
    private Integer deleted;

    /**
     * 租户ID
     */
    private Long tenantId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SurveyDetailDO other = (SurveyDetailDO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSurveyId() == null ? other.getSurveyId() == null : this.getSurveyId().equals(other.getSurveyId()))
            && (this.getOpt() == null ? other.getOpt() == null : this.getOpt().equals(other.getOpt()))
            && (this.getOptionWeighting() == null ? other.getOptionWeighting() == null : this.getOptionWeighting().equals(other.getOptionWeighting()))
            && (this.getScvt() == null ? other.getScvt() == null : this.getScvt().equals(other.getScvt()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getCreator() == null ? other.getCreator() == null : this.getCreator().equals(other.getCreator()))
            && (this.getUpdater() == null ? other.getUpdater() == null : this.getUpdater().equals(other.getUpdater()))
            && (this.getDeleted() == null ? other.getDeleted() == null : this.getDeleted().equals(other.getDeleted()))
            && (this.getTenantId() == null ? other.getTenantId() == null : this.getTenantId().equals(other.getTenantId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSurveyId() == null) ? 0 : getSurveyId().hashCode());
        result = prime * result + ((getOpt() == null) ? 0 : getOpt().hashCode());
        result = prime * result + ((getOptionWeighting() == null) ? 0 : getOptionWeighting().hashCode());
        result = prime * result + ((getScvt() == null) ? 0 : getScvt().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getCreator() == null) ? 0 : getCreator().hashCode());
        result = prime * result + ((getUpdater() == null) ? 0 : getUpdater().hashCode());
        result = prime * result + ((getDeleted() == null) ? 0 : getDeleted().hashCode());
        result = prime * result + ((getTenantId() == null) ? 0 : getTenantId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", surveyId=").append(surveyId);
        sb.append(", opt=").append(opt);
        sb.append(", optionWeighting=").append(optionWeighting);
        sb.append(", scvt=").append(scvt);
        sb.append(", createdTime=").append(createTime);
        sb.append(", updatedTime=").append(updateTime);
        sb.append(", createdBy=").append(creator);
        sb.append(", updatedBy=").append(updater);
        sb.append(", deleteFlag=").append(deleted);
        sb.append(", tenantId=").append(tenantId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}