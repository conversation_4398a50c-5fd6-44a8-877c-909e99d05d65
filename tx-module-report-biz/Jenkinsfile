pipeline {
    agent any
    parameters {
        choice(name: 'ENV', choices: ['dev', 'prod'], description: '选择部署环境')
    }
    tools {
        maven 'M3'
    }
    stages {
        stage('Clean') {
            steps {
                sh 'mvn clean install -U -DskipTests -Dmaven.test.skip=true -Dmaven.test.failure.ignore=true'  // 添加更多跳过测试的参数
            }
        }
        stage('Build') {
            steps {
                sh 'mvn package -DskipTests -Dmaven.test.skip=true -Dmaven.test.failure.ignore=true'  // 添加更多跳过测试的参数
            }
        }
        stage('Deploy') {
            steps {
                script {
                    // 定义部署配置生成函数
                    def createTransfer = { String configName, String remoteDir ->
                        sshPublisherDesc(
                            configName: configName,
                            transfers: [
                                sshTransfer(
                                    sourceFiles: 'tx-module-report-biz/target/tx-module-report-biz.jar',
                                    removePrefix: 'tx-module-report-biz/target',
                                    remoteDirectory: '/opt',
                                    execCommand: '''
                                        systemctl restart tx-module-report.service
                                    '''
                                )
                            ]
                        )
                    }

                    if (params.ENV == 'dev') {
                        // 开发环境部署
                        sshPublisher publishers: [createTransfer('dev-report', '/opt')]
                    } else {
                        // 生产环境多节点部署
                        def prodNodes = [
                            [configName: 'prod-report1', remoteDir: '/opt'],
                            [configName: 'prod-report2', remoteDir: '/opt']
                        ]
                        sshPublisher publishers: prodNodes.collect {
                            createTransfer(it.configName, it.remoteDir)
                        }
                    }
                }
            }
        }
    }
}
