package cn.iocoder.yudao.module.infra.dal.mysql.demo;

import java.util.*;

import pojo.cn.tianxing.cloud.framework.common.PageResult;
import pojo.cn.tianxing.cloud.framework.common.PageParam;
import query.core.mybatis.cn.tianxing.cloud.framework.LambdaQueryWrapperX;
import mapper.core.mybatis.cn.tianxing.cloud.framework.BaseMapperX;
import cn.iocoder.yudao.module.infra.dal.dataobject.demo.InfraStudentTeacherDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 学生班主任 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfraStudentTeacherMapper extends BaseMapperX<InfraStudentTeacherDO> {

    default InfraStudentTeacherDO selectByStudentId(Long studentId) {
        return selectOne(InfraStudentTeacherDO::getStudentId, studentId);
    }

    default int deleteByStudentId(Long studentId) {
        return delete(InfraStudentTeacherDO::getStudentId, studentId);
    }

}