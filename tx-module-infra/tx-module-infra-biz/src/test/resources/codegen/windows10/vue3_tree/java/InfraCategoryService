package cn.iocoder.yudao.module.infra.service.demo;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.infra.controller.admin.demo.vo.*;
import cn.iocoder.yudao.module.infra.dal.dataobject.demo.InfraCategoryDO;
import pojo.cn.tianxing.cloud.framework.common.PageResult;
import pojo.cn.tianxing.cloud.framework.common.PageParam;

/**
 * 分类 Service 接口
 *
 * <AUTHOR>
 */
public interface InfraCategoryService {

    /**
     * 创建分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCategory(@Valid InfraCategorySaveReqVO createReqVO);

    /**
     * 更新分类
     *
     * @param updateReqVO 更新信息
     */
    void updateCategory(@Valid InfraCategorySaveReqVO updateReqVO);

    /**
     * 删除分类
     *
     * @param id 编号
     */
    void deleteCategory(Long id);

    /**
     * 获得分类
     *
     * @param id 编号
     * @return 分类
     */
    InfraCategoryDO getCategory(Long id);

    /**
     * 获得分类列表
     *
     * @param listReqVO 查询条件
     * @return 分类列表
     */
    List<InfraCategoryDO> getCategoryList(InfraCategoryListReqVO listReqVO);

}