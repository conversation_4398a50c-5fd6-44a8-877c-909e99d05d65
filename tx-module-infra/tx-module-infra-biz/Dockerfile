## AdoptOpenJDK 停止发布 OpenJDK 二进制，而 Eclipse Temurin 是它的延伸，提供更好的稳定性
## 感谢复旦核博士的建议！灰子哥，牛皮！
# 基础镜像
FROM eclipse-temurin:17-jre

# 设置时区为Asia/Shanghai
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 指定工作目录
WORKDIR /app

# 将 jar 包添加到工作目录
ADD target/tx-module-infra-biz.jar .

# 暴露端口
EXPOSE 48082

# 启动命令
ENTRYPOINT ["java","-Dspring.cloud.nacos.discovery.namespace=tangpanhui","-jar","/app/tx-module-infra-biz.jar","--spring.profiles.active=dev"]
