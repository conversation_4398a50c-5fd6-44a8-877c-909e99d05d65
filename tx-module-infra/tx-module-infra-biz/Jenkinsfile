pipeline {
    agent any
    parameters {
        choice(name: 'ENV', choices: ['dev', 'prod'], description: '选择部署环境')
    }
    tools {
        maven 'M3'
    }
    stages {
        stage('Clean') {
            steps {
                sh 'mvn clean install -U -DskipTests -Dmaven.test.skip=true -Dmaven.test.failure.ignore=true'
            }
        }
        stage('Build') {
            steps {
                sh 'mvn package -DskipTests -Dmaven.test.skip=true -Dmaven.test.failure.ignore=true'
            }
        }
        stage('Deploy') {
            steps {
                script {
                    def createTransfer = { String configName, String remoteDir ->
                        sshPublisherDesc(
                            configName: configName,
                            transfers: [
                                sshTransfer(
                                    sourceFiles: 'tx-module-infra/tx-module-infra-biz/target/tx-module-infra-biz.jar',
                                    removePrefix: 'tx-module-infra/tx-module-infra-biz/target',
                                    remoteDirectory: '/opt',
                                    execCommand: '''
                                        systemctl restart tx-module-infra.service
                                    '''
                                )
                            ]
                        )
                    }

                    if (params.ENV == 'dev') {
                        sshPublisher publishers: [createTransfer('dev-infra', '/opt')]
                    } else {
                        def prodNodes = [
                            [configName: 'prod-infra1', remoteDir: '/opt'],
                            [configName: 'prod-infra2', remoteDir: '/opt']
                        ]
                        sshPublisher publishers: prodNodes.collect {
                            createTransfer(it.configName, it.remoteDir)
                        }
                    }
                }
            }
        }
    }
}
