package cn.tianxing.cloud.framework.common.util.arithmetic;

import cn.tianxing.cloud.framework.common.enums.TableNameEnum;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 雪花算法工具类
 */
public class SnowflakeUtils {

    // 时间戳基准点 (2020-01-01)
    private static final long EPOCH = 1577808000000L;

    // 位数分配常量
    private static final int TIMESTAMP_BITS = 41;
    private static final int DATA_CENTER_BITS = 5;
    private static final int TABLE_ID_BITS = 3;
    private static final int WORKER_BITS = 7;
    private static final int SEQUENCE_BITS = 12;

    // 位移量计算
    private static final int TABLE_ID_SHIFT = SEQUENCE_BITS;
    private static final int WORKER_SHIFT = SEQUENCE_BITS + TABLE_ID_BITS;
    private static final int DATA_CENTER_SHIFT = SEQUENCE_BITS + TABLE_ID_BITS + WORKER_BITS;
    private static final int TIMESTAMP_SHIFT = SEQUENCE_BITS + TABLE_ID_BITS + WORKER_BITS + DATA_CENTER_BITS;

    /**
     * 工具类：解析ID对应的表名
     */

    // 最大值计算
    private static final long MAX_DATA_CENTER = -1L ^ (-1L << DATA_CENTER_BITS);
    private static final long MAX_WORKER = -1L ^ (-1L << WORKER_BITS);
    private static final long MAX_SEQUENCE = -1L ^ (-1L << SEQUENCE_BITS);
    private static final long MAX_TABLE_ID = -1L ^ (-1L << TABLE_ID_BITS);

    // 表标识映射
    private final Map<String, Long> tableRegistry = new ConcurrentHashMap<>();
    private final Map<Long, String> reverseTableRegistry = new ConcurrentHashMap<>();

    // 实例相关参数
    private final long dataCenterId;
    private final long workerId;
    private long lastTimestamp = -1L;
    private final AtomicLong sequence = new AtomicLong(0);

    /**
     * 初始化雪花算法实例
     *
     * @param dataCenterId 数据中心ID (0-31)
     * @param workerId     工作节点ID (0-127)
     */
    public SnowflakeUtils(long dataCenterId, long workerId) {
        if (dataCenterId < 0 || dataCenterId > MAX_DATA_CENTER) {
            throw new IllegalArgumentException("Data center ID must be between 0 and " + MAX_DATA_CENTER);
        }
        if (workerId < 0 || workerId > MAX_WORKER) {
            throw new IllegalArgumentException("Worker ID must be between 0 and " + MAX_WORKER);
        }
        this.dataCenterId = dataCenterId;
        this.workerId = workerId;

        // 预注册默认表
        registerTable(TableNameEnum.TABLE_SYSTEM_USER, 0);
        registerTable(TableNameEnum.TABLE_MEMBER_USER, 1);
    }

    /**
     * 注册新表类型
     *
     * @param tableName 表名称
     * @param tableId   表标识ID (0-7)
     */
    public void registerTable(String tableName, long tableId) {
        if (tableId < 0 || tableId > MAX_TABLE_ID) {
            throw new IllegalArgumentException("Table ID must be between 0 and " + MAX_TABLE_ID);
        }
        if (tableRegistry.containsKey(tableName) || reverseTableRegistry.containsKey(tableId)) {
            throw new IllegalArgumentException("Table name or ID already registered");
        }
        tableRegistry.put(tableName, tableId);
        reverseTableRegistry.put(tableId, tableName);
    }

    /**
     * 生成带表标识的雪花ID
     *
     * @param tableName 已注册的表名称
     * @return 64位唯一ID
     */
    public synchronized long nextId(String tableName) {
        Long tableId = tableRegistry.get(tableName);
        if (tableId == null) {
            throw new IllegalArgumentException("Unregistered table: " + tableName);
        }

        long currentTime = System.currentTimeMillis();

        // 时钟回拨检测
        if (currentTime < lastTimestamp) {
            throw new RuntimeException("Clock moved backwards. Refusing to generate ID");
        }

        // 同一毫秒内的序列处理
        if (currentTime == lastTimestamp) {
            long seq = sequence.incrementAndGet() & MAX_SEQUENCE;
            if (seq == 0) {
                currentTime = tilNextMillis(lastTimestamp);
            }
        } else {
            sequence.set(0);
        }

        lastTimestamp = currentTime;

        // ID组合算法
        return ((currentTime - EPOCH) << TIMESTAMP_SHIFT) | (dataCenterId << DATA_CENTER_SHIFT) | (tableId << TABLE_ID_SHIFT) | (workerId << WORKER_SHIFT) | sequence.get();
    }

    /**
     * 工具类：解析ID对应的表名
     */
    // 位运算常量（与生成器匹配）
    private static final long TABLE_ID_MASK = 0x07L;  // 二进制111

    /**
     * 从ID中提取表名称
     *
     * @param id 雪花算法生成的ID
     * @return 对应的表名称
     */
    public static String getTableName(long id) {
        long tableId = (id >>> TABLE_ID_SHIFT) & TABLE_ID_MASK;

        // 实际项目中应从注册中心获取映射
        switch ((int) tableId) {
            case 0:
                return TableNameEnum.TABLE_SYSTEM_USER;
            case 1:
                return TableNameEnum.TABLE_MEMBER_USER;
            // 预留扩展
            default:
                return "unknown_table_" + tableId;
        }
    }

    // 阻塞至下一毫秒
    private long tilNextMillis(long lastTimestamp) {
        long timestamp = System.currentTimeMillis();
        while (timestamp <= lastTimestamp) {
            timestamp = System.currentTimeMillis();
        }
        return timestamp;
    }
}
