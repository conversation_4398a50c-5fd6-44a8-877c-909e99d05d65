package cn.tianxing.cloud.module.member.dal.mysql.user;

import cn.tianxing.cloud.module.member.dal.mysql.user.MemberUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * 查询会员表行数的临时工具类
 */
@SpringBootApplication
@ComponentScan(basePackages = "cn.tianxing.cloud")
public class MemberCountQuery implements CommandLineRunner {

    @Autowired
    private MemberUserMapper memberUserMapper;

    public static void main(String[] args) {
        SpringApplication.run(MemberCountQuery.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        try {
            Long count = memberUserMapper.selectCount(null);
            System.out.println("===========================================");
            System.out.println("会员表(member_user)总行数: " + count);
            System.out.println("===========================================");
        } catch (Exception e) {
            System.err.println("查询失败: " + e.getMessage());
            e.printStackTrace();
        }
        System.exit(0);
    }
}
