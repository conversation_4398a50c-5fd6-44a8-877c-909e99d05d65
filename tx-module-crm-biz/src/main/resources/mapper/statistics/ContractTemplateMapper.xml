<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.tianxing.cloud.module.crm.dal.mysql.contract.ContractTemplateMapper">

    <resultMap id="BaseResultMap" type="cn.tianxing.cloud.module.crm.dal.dataobject.contract.ContractTemplateDO">
            <id property="id" column="id" />
            <result property="contractType" column="contract_type" />
            <result property="templateName" column="template_name" />
            <result property="templateStatus" column="template_status" />
            <result property="filePath" column="file_path" />
            <result property="relatedContracts" column="related_contracts" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="creator" column="creator" />
            <result property="updater" column="updater" />
            <result property="deleted" column="deleted" />
            <result property="tenantId" column="tenant_id" />
    </resultMap>

    <sql id="Base_Column_List">
        id,contract_type,template_name,template_status,file_path,related_contracts,
        create_time,update_time,creator,updater,deleted,
        tenant_id
    </sql>
    <select id="getUrl" resultType="java.lang.String">
        select file_path from contract_template where id = #{templateId}
    </select>
</mapper>
