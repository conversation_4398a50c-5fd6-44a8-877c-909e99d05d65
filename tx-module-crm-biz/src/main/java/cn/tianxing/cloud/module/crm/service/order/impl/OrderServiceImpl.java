package cn.tianxing.cloud.module.crm.service.order.impl;

import cn.tianxing.cloud.framework.common.pojo.CommonResult;
import cn.tianxing.cloud.module.crm.controller.admin.order.vo.OrderCreateReqVO;
import cn.tianxing.cloud.module.crm.dal.dataobject.caseentrust.CaseEntrustDO;
import cn.tianxing.cloud.module.crm.dal.dataobject.contract.ContractBasicInfoDO;
import cn.tianxing.cloud.module.crm.dal.dataobject.contract.RepaymentPlanDO;
import cn.tianxing.cloud.module.crm.dal.dataobject.repayment.DebtRepaymentDetailsDO;
import cn.tianxing.cloud.module.crm.dal.dataobject.user.MallUserMappingDO;
import cn.tianxing.cloud.module.crm.dal.mysql.caseentrust.CaseEntrustMapper;
import cn.tianxing.cloud.module.crm.dal.mysql.contract.ContractBasicInfoMapper;
import cn.tianxing.cloud.module.crm.dal.mysql.contract.RepaymentPlanMapper;
import cn.tianxing.cloud.module.crm.dal.mysql.repayment.DebtRepaymentDetailsMapper;
import cn.tianxing.cloud.module.crm.service.order.OrderService;
import cn.tianxing.cloud.module.crm.service.user.MallUserMappingService;
import cn.tianxing.cloud.module.member.api.user.MemberUserApi;
import cn.tianxing.cloud.module.member.api.user.dto.MemberUserRespDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

/**
 * 订单 Service 实现类
 */
@Slf4j
@Service
@Validated
public class OrderServiceImpl implements OrderService {

    @Resource
    private MallUserMappingService mallUserMappingService;

    @Resource
    private MemberUserApi memberUserApi;

    @Resource
    private CaseEntrustMapper caseEntrustMapper;

    @Resource
    private ContractBasicInfoMapper contractBasicInfoMapper;

    @Resource
    private RepaymentPlanMapper repaymentPlanMapper;

    @Resource
    private DebtRepaymentDetailsMapper debtRepaymentDetailsMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> processOrder(OrderCreateReqVO createReqVO) {
        // 新增：根据order_number和refund_status查询debt_repayment_details表
        DebtRepaymentDetailsDO existDetail = debtRepaymentDetailsMapper.selectByOrderNumberAndRefundStatus(createReqVO.getOrderNumber(), createReqVO.getRefundStatus());
        if (existDetail != null) {
            return CommonResult.success(true);
        }
        log.info("处理订单：{}", createReqVO);
        if ((createReqVO.getOrderStatus().equals(6) || createReqVO.getOrderStatus().equals(5))
            && (createReqVO.getRefundStatus() != null && (createReqVO.getRefundStatus() == 0 || createReqVO.getRefundStatus() == 2 || createReqVO.getRefundStatus() == 3))) {
            // 1. 查询用户映射
            MallUserMappingDO mallUserMapping = mallUserMappingService.getMallUserMappingByPhone(createReqVO.getPhone());
            Long userMemberId;

            // 2. 如果没有查询到数据，就查询会员用户表
            if (mallUserMapping == null) {
                // 通过手机号查询会员用户
                MemberUserRespDTO memberUser = memberUserApi.getUserByMobile(createReqVO.getPhone()).getCheckedData();

                // 如果找到会员用户，创建映射关系
                if (memberUser != null) {
                    mallUserMapping = mallUserMappingService.createMallUserMapping(
                            createReqVO.getMemberUserId(),
                            memberUser.getId(),
                            createReqVO.getPhone());
                    userMemberId = memberUser.getId();
                } else {
                    log.error("会员用户不存在，手机号：{}", createReqVO.getPhone());
                    // 如果没有找到会员用户，无法处理
                    return CommonResult.error(40001, "会员用户不存在，手机号：" + createReqVO.getPhone());
                }
            } else {
                userMemberId = mallUserMapping.getUserMemberId();
            }

            // 3. 根据userMemberId查询case_entrust表，按创建时间降序获取最新的一条
            LambdaQueryWrapper<CaseEntrustDO> caseEntrustQueryWrapper = new LambdaQueryWrapper<CaseEntrustDO>()
                    .eq(CaseEntrustDO::getDebtorMemberId, userMemberId)
                    .orderByDesc(CaseEntrustDO::getCreateTime)
                    .last("LIMIT 1"); // 限制只返回一条记录
            CaseEntrustDO caseEntrust = caseEntrustMapper.selectOne(caseEntrustQueryWrapper);
            if (caseEntrust == null) {
                log.error("没有找到对应的委案信息，手机号：{}", createReqVO.getPhone());
                return CommonResult.error(40002, "没有找到对应的委案信息，手机号：" + createReqVO.getPhone());
            }
            Long caseEntrustId = caseEntrust.getId();

            // 4. 根据委案id查询contract_basic_info，按创建时间降序获取最新的一条
            LambdaQueryWrapper<ContractBasicInfoDO> contractQueryWrapper = new LambdaQueryWrapper<ContractBasicInfoDO>()
                    .eq(ContractBasicInfoDO::getCaseEntrustId, caseEntrustId)
                    .orderByDesc(ContractBasicInfoDO::getCreateTime)
                    .last("LIMIT 1"); // 限制只返回一条记录
            ContractBasicInfoDO contractBasicInfo = contractBasicInfoMapper.selectOne(contractQueryWrapper);
            if (contractBasicInfo == null) {
                log.error("没有找到对应的合同信息，手机号：{}", createReqVO.getPhone());
                return CommonResult.error(40003, "没有找到对应的合同信息，手机号：" + createReqVO.getPhone());
            }
            Long contractId = contractBasicInfo.getId();

            // 5. 处理金额逻辑
            BigDecimal mount = createReqVO.getMount();

            // 根据type参数处理mount的正负值
            // type：0收入 1支出
            if (createReqVO.getType() == 1) {
                // 如果是支出，将mount转为负数
                mount = mount.negate();
            }
            // 此时mount为正数表示收入（增加债务还款），负数表示支出（减少债务还款）

            BigDecimal remainingDebtAmount = contractBasicInfo.getRemainingDebtAmount();

            // 如果是收入且mount > remaining_debt_amount，则调整金额
            if (mount.compareTo(BigDecimal.ZERO) > 0 && mount.compareTo(remainingDebtAmount) > 0) {
                BigDecimal remainMount = mount.subtract(remainingDebtAmount);
                mount = remainingDebtAmount;
                // 这里可以处理剩余金额remainMount的逻辑，如记录或其他处理
            }

            // 6. 更新合同表的已化债金额和剩余化债金额
            BigDecimal convertedDebtAmount = contractBasicInfo.getConvertedDebtAmount().add(mount);
            BigDecimal newRemainingDebtAmount = contractBasicInfo.getRemainingDebtAmount().subtract(mount);

            // 确保剩余化债金额不小于0
            if (newRemainingDebtAmount.compareTo(BigDecimal.ZERO) < 0) {
                newRemainingDebtAmount = BigDecimal.ZERO;
            }

            contractBasicInfo.setConvertedDebtAmount(convertedDebtAmount);
            contractBasicInfo.setRemainingDebtAmount(newRemainingDebtAmount);
            contractBasicInfoMapper.updateById(contractBasicInfo);

            // 7. 查询还款计划表并更新
            // 从create_time中提取年月格式
            String yearMonthStr = null;
            if (createReqVO.getCreateTime() != null && !createReqVO.getCreateTime().isEmpty()) {
                try {
                    // 解析传入的日期时间字符串
                    DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    LocalDateTime dateTime = LocalDateTime.parse(createReqVO.getCreateTime(), inputFormatter);

                    // 提取年月部分
                    YearMonth yearMonth = YearMonth.from(dateTime);
                    yearMonthStr = yearMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
                } catch (Exception e) {
                    // 日期格式解析异常，可以记录日志
                    // 如果解析失败，继续使用默认查询方式
                }
            }

            // 构建查询条件
            LambdaQueryWrapper<RepaymentPlanDO> queryWrapper = new LambdaQueryWrapper<RepaymentPlanDO>()
                    .eq(RepaymentPlanDO::getContractId, contractId);

            // 如果成功解析出年月，则添加到查询条件
            if (yearMonthStr != null) {
                queryWrapper.apply("DATE_FORMAT(due_date, '%Y-%m') = {0}", yearMonthStr);
            }

            // 执行查询
            RepaymentPlanDO repaymentPlan = repaymentPlanMapper.selectOne(queryWrapper);

            if (repaymentPlan != null) {
                // 更新实际债务金额和未结清金额
                BigDecimal actualDebtAmount = repaymentPlan.getActualDebtAmount().add(mount);
                BigDecimal outstandingAmount = repaymentPlan.getOutstandingAmount().subtract(mount);

                // 如果未结清金额小于0，设置为0
                if (outstandingAmount.compareTo(BigDecimal.ZERO) < 0) {
                    outstandingAmount = BigDecimal.ZERO;
                }

                // 确保实际债务金额不小于0
                if (actualDebtAmount.compareTo(BigDecimal.ZERO) < 0) {
                    actualDebtAmount = BigDecimal.ZERO;
                }

                repaymentPlan.setActualDebtAmount(actualDebtAmount);
                repaymentPlan.setOutstandingAmount(outstandingAmount);
                repaymentPlanMapper.updateById(repaymentPlan);
            }

            // 8. 插入债务还款明细记录
            DebtRepaymentDetailsDO debtRepaymentDetails = DebtRepaymentDetailsDO.builder()
                    .contractId(contractId)
                    .caseEntrustId(caseEntrustId)
                    .repaymentPlanId(repaymentPlan != null ? repaymentPlan.getId() : null)
                    .memberUserId(userMemberId)  // 使用userMemberId而不是传入的memberUserId
                    .description(createReqVO.getDescription())
                    .orderNumber(createReqVO.getOrderNumber())
                    .mount(createReqVO.getMount().abs())  // 存储金额的绝对值
                    .type(createReqVO.getType())
                    .debtType(createReqVO.getDebtType())
                    .refundStatus(createReqVO.getRefundStatus())
                    .build();
            debtRepaymentDetailsMapper.insert(debtRepaymentDetails);

            return CommonResult.success(true);
        } else {
            log.error("状态先不处理，orderStatus：{}，refundStatus：{}", createReqVO.getOrderStatus(), createReqVO.getRefundStatus());
            return CommonResult.error(40004, "状态先不处理，orderStatus：" + createReqVO.getOrderStatus() + ", refundStatus：" + createReqVO.getRefundStatus());
        }
    }
} 