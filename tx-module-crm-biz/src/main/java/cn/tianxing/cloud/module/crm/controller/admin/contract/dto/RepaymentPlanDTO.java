package cn.tianxing.cloud.module.crm.controller.admin.contract.dto;

import cn.tianxing.cloud.framework.common.pojo.PageParam;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(description = "管理后台 - 还款计划分页查询参数")
public class RepaymentPlanDTO extends PageParam {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联合同ID
     */
    @Schema(description = "关联合同ID")
    private Long contractId;

    /**
     * 当前期数（如：1）
     */
    @Schema(description = "当前期数")
    private Integer periodSeq;

    /**
     * 总期数（如：54）
     */
    @Schema(description = "总期数")
    private Integer totalPeriods;

    /**
     * 应还款日期（格式：YYYY-MM）
     */
    @Schema(description = "应还款月")
    @JsonFormat(pattern ="yyyy-MM")
    private Date dueDate;

    /**
     * 计划还款本金
     */
    @Schema(description = "计划还款本金")
    private BigDecimal principal;

    /**
     * 利息
     */
    @Schema(description = "利息")
    private BigDecimal interest;

    /**
     * 还款状态（1-待还款 2-已还款 3-逾期）
     */
    @Schema(description = "还款状态")
    private Integer statusCode;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String creator;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updater;

    /**
     * 删除标志（0：未删除，1：已删除）
     */
    @Schema(description = "删除标志")
    @TableLogic
    private Integer deleted;

    private Integer offset;
}
