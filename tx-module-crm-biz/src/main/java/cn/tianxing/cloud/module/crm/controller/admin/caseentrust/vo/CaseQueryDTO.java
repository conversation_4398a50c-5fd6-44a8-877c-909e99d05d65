package cn.tianxing.cloud.module.crm.controller.admin.caseentrust.vo;

import cn.tianxing.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.tianxing.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 委案信息 查询")
@Data
public class CaseQueryDTO extends PageParam {

    @Schema(description = "委案编号", example = "10795")
    private String caseNumber;

    @Schema(description = "逾期阶段（来自字典 case_overdue_stage）")
    private String caseStage;

    @Schema(description = "债务人", example = "10795")
    private String debtor;

    @Schema(description = "案件等级（来自字典 case_level）")
    private String caseLevel;

    @Schema(description = "案件状态（来自字典 case_level）")
    private String caseState;

    @Schema(description = "分配状态 （来自字典 case_allocation_status）")
    private String allocationState;

    @Schema(description = "留案状态 （来自字典 case_retain_status）")
    private String courtRetentionState;

    @Schema(description = "业务员id", example = "0010")
    private Long salesman;

    @Schema(description = "案件来源 （来自字典 case_source）")
    private String sourceCase;

    @Schema(description = "债权人", example = "10795")
    private String creditor;

    @Schema(description = "最小委案金额", example = "10795")
    private BigDecimal minAmount;

    @Schema(description = "最大委案金额", example = "10795")
    private BigDecimal maxAmount;

    @Schema(description = "最小减免额", example = "10795")
    private BigDecimal minReduction;

    @Schema(description = "最大减免额", example = "10795")
    private BigDecimal maxReduction;

    @Schema(description = "分配开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime allocationBeginTime;

    @Schema(description = "分配结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime allocationEndTime;

    @Schema(description = "债权关联用户ID")
    private Long creditorMemberUserId;

    @Schema(description = "债务关联用户ID")
    private Long debtorMemberUserId;

    @Schema(description = "债权关联状态 字典（case_user_association_status）")
    private String creditorAssociationStatus;

    @Schema(description = "债务关联状态 字典（case_user_association_status）")
    private String debtorAssociationStatus;
    
    @Schema(description = "债权人职务")
    private String creditorJobTitle;
    
    @Schema(description = "债务人职务")
    private String debtorJobTitle;
}