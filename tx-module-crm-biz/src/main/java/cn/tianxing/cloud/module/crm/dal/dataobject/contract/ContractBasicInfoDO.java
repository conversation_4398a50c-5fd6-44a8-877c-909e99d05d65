package cn.tianxing.cloud.module.crm.dal.dataobject.contract;

import cn.tianxing.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import static cn.tianxing.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 合同基本信息表
 *
 * @TableName contract_basic_info
 */
@TableName(value = "contract_basic_info")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContractBasicInfoDO extends BaseDO implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "合同主键id")
    private Long id;

    @Schema(description = "案件委托ID")
    private Long caseEntrustId;

    @Schema(description = "关联委案编号")
    @TableField(exist = false)
    private String caseNumber;
    /**
     * 模板id
     */
    @Schema(description = "模板ID")
    private Long templateId;


    /**
     * 合同类型
     */
    @Schema(description = "合同类型")
    private String contractType;

    /**
     * 模板名称
     */
    @Schema(description = "模板名称")
    private String templateName;

    /**
     * 合同编号（唯一）
     */
    @Schema(description = "合同编号（唯一）")
    private String contractNumber;

    /**
     * 合同名称
     */
    @Schema(description = "合同名称")
    private String contractName;

    /**
     * 合同内网url
     */
    @Schema(description = "合同内网URL")
    private String contractUrl;

    /**
     * 化债总金额（元）
     */
    @Schema(description = "化债总金额（元）")
    private BigDecimal totalDebtReductionAmount;

    /**
     * 实际减免金额（元）
     */
    @Schema(description = "实际减免金额（元）")
    private BigDecimal actualReductionAmount;

    /**
     * 实际化债总额（元）
     */
    @Schema(description = "实际化债总额（元）")
    private BigDecimal actualDebtReductionAmount;

    /**
     * 每月应化债金额（元）
     */
    @Schema(description = "每月应化债金额（元）")
    private BigDecimal monthlyDebtReductionAmount;

    @Schema(description = "已化债金额（元）")
    private BigDecimal convertedDebtAmount;


    @Schema(description = "剩余化债金额（元）")
    private BigDecimal remainingDebtAmount;

    /**
     * 化债期数（月，必须>0）
     */
    @Schema(description = "化债期数（月，必须>0）")
    private Integer debtReductionPeriods;

    /**
     * 合同开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "合同开始日期")
    private Date startDate;

    /**
     * 甲方名称
     */
    @Schema(description = "甲方名称")
    private String partyAName;

    /**
     * 甲方统一社会信用代码
     */
    @Schema(description = "甲方统一社会信用代码")
    private String partyAUnifiedSocialCreditCode;

    /**
     * 甲方法定代表人
     */
    @Schema(description = "甲方法定代表人")
    private String partyALegalRepresentative;

    /**
     * 法定代表人联系方式
     */
    @Schema(description = "法定代表人联系方式")
    private String partyAContactNumber;

    /**
     * 甲方地址
     */
    @Schema(description = "甲方地址")
    private String partyAAddress;

    /**
     * 乙方名称
     */
    @Schema(description = "乙方名称")
    private String partyBName;

    /**
     * 乙方统一社会信用代码
     */
    @Schema(description = "乙方统一社会信用代码")
    private String partyBUnifiedSocialCreditCode;

    /**
     * 乙方法定代表人
     */
    @Schema(description = "乙方法定代表人")
    private String partyBLegalRepresentative;

    /**
     * 法定代表人联系方式
     */
    @Schema(description = "法定代表人联系方式")
    private String partyBContactNumber;

    /**
     * 乙方地址
     */
    @Schema(description = "乙方地址")
    private String partyBAddress;

    /**
     * 丙方名称
     */
    @Schema(description = "丙方名称")
    private String partyCName;

    /**
     * 丙方证件号码/统一社会信用代码
     */
    @Schema(description = "丙方证件号码/统一社会信用代码")
    private String partyCIdNumberOrUnifiedSocialCreditCode;

    /**
     * 丙方联系方式
     */
    @Schema(description = "丙方联系方式")
    private String partyCContactNumber;

    /**
     * 甲方签订状态
     */
    @Schema(description = "甲方签订状态")
    private Integer partyASignStatus;

    /**
     * 乙方签订状态
     */
    @Schema(description = "乙方签订状态")
    private Integer partyBSignStatus;

    /**
     * 丙方签订状态
     */
    @Schema(description = "丙方签订状态")
    private Integer partyCSignStatus;

    /**
     * 甲方签订时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @Schema(description = "甲方签订时间")
    private LocalDateTime partyATime;

    /**
     * 乙方签订时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @Schema(description = "乙方签订时间")
    private LocalDateTime partyBTime;

    /**
     * 丙方签订时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @Schema(description = "丙方签订时间")
    private LocalDateTime partyCTime;

    /**
     * 合同状态
     * 0, 草稿
     * 1, 审批中
     * 2, 审批通过
     * 3, 审批驳回
     * 4, 作废
     * 5, 履约中
     * 6, 结束
     * 7, 变更
     */
    @Schema(description = "合同状态: 草稿 draft\n" +
            "审批中 in_approval\n" +
            "审批通过 end_approval\n" +
            "审批驳回 back_approval\n" +
            "作废 cancel\n" +
            "履约中 in_performance\n" +
            "合同结束 end\n" +
            "变更 change")
    private String status;

    /**
     * 审批人ID
     */
    @Schema(description = "审批人ID")
    private Long approverId;

    /**
     * 审批人姓名
     */
    @TableField(exist = false)
    @Schema(description = "审批人姓名")
    private String approveName;

    /**
     * 审批时间
     */
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "审批时间")
    private LocalDateTime approvalTime;

    /**
     * 审批描述
     */
    @Schema(description = "审批描述")
    private String approvalRemark;

    /**
     * 丙方地址
     */
    @Schema(description = "丙方地址")
    private String partyCAddress;

    @TableField(exist = false)
    @Schema(description = "序列化版本标识符")
    private static final long serialVersionUID = 1L;

}