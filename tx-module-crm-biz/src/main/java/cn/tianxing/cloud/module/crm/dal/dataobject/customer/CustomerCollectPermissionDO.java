package cn.tianxing.cloud.module.crm.dal.dataobject.customer;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * 信息收集表授权表
 * @TableName customer_collect_permission
 */
@Data
@Accessors(chain = true)
@TableName("customer_collect_permission")
public class CustomerCollectPermissionDO implements Serializable {
    /**
     *
     */
    private Long id;

    /**
     * 客户信息id
     */
    @Schema(description = "客户信息id")
    private Long customerCollectId;

    /**
     * 申请人id
     */
    @Schema(description = "申请人id")
    private Long applyId;

    @Schema(description = "申请人姓名")
    @TableField(exist = false)
    private String applyName;

    @Schema(description = "审批人id")
    private Long approvalId;

    @Schema(description = "审批人姓名")
    @TableField(exist = false)
    private String approvalName;

    /**
     * 审批状态，1通过，2未通过
     */
    @Schema(description = "审批是否通过 字典类型：customer_approval_flag")
    private String passFlag;

    /**
     * 申请状态，0审批中，1审批结束
     */
    @Schema(description = "申请状态，字典类型：customer_approval_status")
    private String status;

    /**
     * 有效时间
     */
    @Schema(description = "有效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date effectiveTime;

    /**
     * 任务类别
     */
    @Schema(description = "任务类别")
    private String taskType;

    /**
     * 任务名称
     */
    @Schema(description = "任务名称")
    private String taskName;

    /**
     * 任务名称
     */
    @Schema(description = "任务名称")
    private String taskContent;

    /**
     * 补充说明
     */
    @Schema(description = "补充说明")
    private String remark;

    /**
     * 甲方(债权人)名称
     */
    @Schema(description = "甲方名称")
    @TableField(exist = false)
    private String partyAName;

    /**
     * 甲方(债权人)证件号码
     */
    @Schema(description = "甲方证件号码")
    @TableField(exist = false)
    private String partyACertNo;

    /**
     * 甲方(债权人)联系电话
     */
    @Schema(description = "甲方联系电话")
    @TableField(exist = false)
    private String partyAPhone;

    /**
     * 甲方(债权人)地址
     */
    @Schema(description = "甲方地址")
    @TableField(exist = false)
    private String partyAAddress;

    /**
     * 乙方(公司)名称
     */
    @Schema(description = "乙方名称")
    @TableField(exist = false)
    private String partyBName;

    /**
     * 乙方(公司)证件号码
     */
    @Schema(description = "乙方证件号码")
    @TableField(exist = false)
    private String partyBCertNo;

    /**
     * 乙方(公司)联系电话
     */
    @Schema(description = "乙方联系电话")
    @TableField(exist = false)
    private String partyBPhone;

    /**
     * 乙方(公司)地址
     */
    @Schema(description = "乙方地址")
    @TableField(exist = false)
    private String partyBAddress;

    /**
     * 丙方(债务人)名称
     */
    @Schema(description = "丙方名称")
    @TableField(exist = false)
    private String partyCName;

    /**
     * 丙方(债务人)证件号码
     */
    @Schema(description = "丙方证件号码")
    @TableField(exist = false)
    private String partyCCertNo;

    /**
     * 丙方(债务人)联系电话
     */
    @Schema(description = "丙方联系电话")
    @TableField(exist = false)
    private String partyCPhone;

    /**
     * 丙方(债务人)地址
     */
    @Schema(description = "丙方地址")
    @TableField(exist = false)
    private String partyCAddress;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDate applyTime;


    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 创建人
     */
    @Schema(description = "申请人")
    private String creator;

    @Schema(description = "申请人姓名")
    @TableField(exist = false)
    private String creatorName;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updater;

    /**
     * 删除标志（0：未删除，1：已删除）
     */
    @Schema(description = "删除标志（0：未删除，1：已删除）")
    @TableLogic
    private Integer deleted;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;

    private static final long serialVersionUID = 1L;

}