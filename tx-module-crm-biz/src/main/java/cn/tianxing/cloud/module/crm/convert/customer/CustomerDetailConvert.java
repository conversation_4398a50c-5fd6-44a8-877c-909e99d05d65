package cn.tianxing.cloud.module.crm.convert.customer;

import cn.tianxing.cloud.framework.common.util.collection.CollectionUtils;
import cn.tianxing.cloud.framework.common.util.object.BeanUtils;
import cn.tianxing.cloud.module.crm.controller.admin.customer.dto.CustomerCollectDetailSaveDTO;
import cn.tianxing.cloud.module.crm.dal.dataobject.customer.CustomerCollectDetailDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface CustomerDetailConvert {

    CustomerDetailConvert INSTANCE = Mappers.getMapper(CustomerDetailConvert.class);

    /**
     * 批量转换
     *
     * @param list 客户信息收集详情列表
     * @return
     */
    default List<CustomerCollectDetailSaveDTO> convertList(List<CustomerCollectDetailDO> list) {
        return CollectionUtils.convertList(list, collect -> convert(collect));
    }

    /**
     * 单独转换
     *
     * @param collectDetailDO 客户信息详情
     * @return
     */
    default CustomerCollectDetailSaveDTO convert(CustomerCollectDetailDO collectDetailDO) {
        CustomerCollectDetailSaveDTO collectDetailSaveDTO = BeanUtils.toBean(collectDetailDO, CustomerCollectDetailSaveDTO.class);
        return collectDetailSaveDTO;
    }

}
