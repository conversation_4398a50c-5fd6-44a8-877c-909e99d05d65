package cn.tianxing.cloud.module.crm.controller.admin.contract.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;


@Schema(description = "管理后台 - 合同签订参数")
@Data
public class ContractBasicInfoSigendSavDTO {

    @Schema(description = "合同主键ID集合")
    @NotEmpty(message = "合同主键ID集合不能为空")
    private List<Long> ids;

}
