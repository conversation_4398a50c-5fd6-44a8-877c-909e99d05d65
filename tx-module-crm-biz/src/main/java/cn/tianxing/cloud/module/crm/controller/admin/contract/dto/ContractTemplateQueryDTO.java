package cn.tianxing.cloud.module.crm.controller.admin.contract.dto;

import cn.tianxing.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static cn.tianxing.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 合同模板分页查询参数")
@Data
public class ContractTemplateQueryDTO extends PageParam {

    @Schema(description = "合同类型，来自字典 contract_type")
    private String contractType;

    @Schema(description = "模板名称，模糊匹配")
    private String templateName;

    @Schema(description = "状态 0禁用 1启用")
    private Integer templateStatus;

    @Schema(description = "修改时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] updateTime;

}
