package cn.tianxing.cloud.module.crm.service.policydistribution;

import cn.tianxing.cloud.module.crm.dal.dataobject.policydistribution.BusinessStaffScopeDetailDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
public interface IBusinessStaffScopeDetailService extends IService<BusinessStaffScopeDetailDO> {

    /**
     * App-获取业务员ID
     *
     * @param addressCodes 省市区地址编号
     * @return
     */
    Long getBusinessStaffId(ArrayList<String> addressCodes);

    /**
     * 获取业务员范围集合
     *
     * @param businessInfoId 业务员主键ID
     */
    List<String> getBusinessStaffScopeDetailList(Long businessInfoId);
    
}
