package cn.tianxing.cloud.module.crm.service.contract;

import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.module.crm.controller.admin.contract.dto.ContractTemplateQueryDTO;
import cn.tianxing.cloud.module.crm.controller.admin.contract.vo.ContractTemplateVO;
import cn.tianxing.cloud.module.crm.dal.dataobject.contract.ContractTemplateDO;
import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.validation.Valid;

/**
* <AUTHOR>
* @description 针对表【contract_template(合同模板主表)】的数据库操作Service
* @createDate 2025-05-24 10:16:14
*/
public interface ContractTemplateService extends IService<ContractTemplateDO> {

    PageResult<ContractTemplateVO> selectPage(@Valid ContractTemplateQueryDTO templateQueryDTO);

    /**
     * 保存合同模板及其附加信息
     *
     * @param template 合同模板信息
     * @return 合同模板ID
     */
    Long saveTemplate(ContractTemplateDO template);

    /**
     * 删除合同模板及其附加信息
     *
     * @param id 合同模板ID
     * @return 是否成功
     */
    boolean removeTemplate(Long id);

    /**
     * 更新合同模板及其附加信息
     *
     * @param template 合同模板信息
     * @return 是否成功
     */
    boolean updateTemplate(ContractTemplateDO template);

    /**
     * 获取合同模板详情
     *
     * @param id 合同模板ID
     * @return 合同模板详情
     */
    ContractTemplateDO getTemplateDetail(Long id);
}
