package cn.tianxing.cloud.module.crm.controller.app.feedback;

import cn.tianxing.cloud.framework.common.pojo.CommonResult;
import cn.tianxing.cloud.module.crm.controller.admin.feedback.vo.FeedbackVO;
import cn.tianxing.cloud.module.crm.controller.app.feedback.dto.FeedbackAppDTO;
import cn.tianxing.cloud.module.crm.service.feedback.FeedbackService;
import cn.tianxing.cloud.module.crm.service.feedback.FeedbackUrlService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.tianxing.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "APP - 建议与反馈")
@RestController
@RequestMapping("/crm/app/feedback")
@Validated
public class FeedbackController {

    @Resource
    private FeedbackService feedbackService;
    
    @Resource
    private FeedbackUrlService feedbackUrlService;

    @PostMapping("/submit")
    @Operation(summary = "提交建议反馈")
    public CommonResult<Boolean> submitFeedback(@RequestBody @Validated FeedbackAppDTO feedbackAppDTO) {
        feedbackService.createFeedback(feedbackAppDTO);
        return success(true);
    }


    @GetMapping("/list-by-user")
    @Operation(summary = "获取历史反馈")
    public CommonResult<List<FeedbackVO>> listByUserId() {
        return success(feedbackService.listByUserId());
    }

    @GetMapping("/detail/{id}")
    @Operation(summary = "获取反馈详情及关联URL")
    public CommonResult<FeedbackVO> getFeedbackDetail(@PathVariable Long id) {
        FeedbackVO feedbackVO = feedbackService.getFeedbackDetail(id);
        if (feedbackVO != null) {
            feedbackVO.setAttachments(feedbackService.getFeedbackAttachmentUrls(id));
        }
        return success(feedbackVO);
    }
}