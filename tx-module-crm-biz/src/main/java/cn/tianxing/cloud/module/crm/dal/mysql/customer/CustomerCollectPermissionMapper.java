package cn.tianxing.cloud.module.crm.dal.mysql.customer;

import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.tianxing.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tianxing.cloud.module.crm.controller.admin.customer.dto.PermissionCollectQueryDTO;
import cn.tianxing.cloud.module.crm.dal.dataobject.customer.CustomerCollectPermissionDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @description 针对表【customer_collect_permission(信息收集表授权表)】的数据库操作Mapper
 * @createDate 2025-04-28 13:53:10
 * @Entity cn.tianxing.cloud.module.crm.dal.dataobject.customer.CustomerCollectPermissionDO
 */
@Mapper
public interface CustomerCollectPermissionMapper extends BaseMapperX<CustomerCollectPermissionDO> {

    default PageResult<CustomerCollectPermissionDO> selectPage(PermissionCollectQueryDTO query) {
        return selectPage(query, new LambdaQueryWrapperX<CustomerCollectPermissionDO>()
                .likeIfPresent(CustomerCollectPermissionDO::getTaskType, query.getTaskType())
                .likeIfPresent(CustomerCollectPermissionDO::getTaskName, query.getTaskName())
                .eqIfPresent(CustomerCollectPermissionDO::getPassFlag, query.getPassFlag())
                .eqIfPresent(CustomerCollectPermissionDO::getStatus, query.getStatus())
                .betweenIfPresent(CustomerCollectPermissionDO::getApplyTime, query.getApplyTime())
                .and(qw -> qw
                        .eq(CustomerCollectPermissionDO::getApprovalId, query.getApplicant())
                        .or()
                        .eq(CustomerCollectPermissionDO::getApplyId, query.getApplicant())
                )
                .orderByDesc(CustomerCollectPermissionDO::getUpdateTime)
        );
    }

}




