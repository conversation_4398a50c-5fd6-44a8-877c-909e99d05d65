package cn.tianxing.cloud.module.crm.controller.admin.caseentrust.dto;

import cn.tianxing.cloud.framework.common.pojo.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Schema(description = "管理后台 - 委案分配列表Query")
@Data
public class CaseEntrustAssignQuery extends PageParam {

    @Schema(description = "委案编号")
    private String caseNumber;

    @Schema(description = "逾期阶段")
    private String caseStage;

    @Schema(description = "业务员")
    private Long salesman;

    @Schema(description = "案件等级")
    private String caseLevel;

    @Schema(description = "案件来源")
    private String sourceCase;

    @Schema(description = "分配状态")
    private Integer allocationState;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}
