package cn.tianxing.cloud.module.crm.controller.admin.policydistribution;


import cn.tianxing.cloud.framework.common.pojo.CommonResult;
import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.module.crm.controller.admin.policydistribution.dto.*;
import cn.tianxing.cloud.module.crm.controller.admin.policydistribution.vo.BusinessStaffInfoPullDownVo;
import cn.tianxing.cloud.module.crm.controller.admin.policydistribution.vo.BusinessStaffPageVo;
import cn.tianxing.cloud.module.crm.controller.admin.policydistribution.vo.BusinessStaffScopeDetailVo;
import cn.tianxing.cloud.module.crm.service.policydistribution.IBusinessStaffInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.tianxing.cloud.framework.common.pojo.CommonResult.success;

/**
 * <p>
 * 业务员信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Tag(name = "管理后台 - 业务员管理")
@RestController
@RequestMapping("/crm/business")
@Validated
@Slf4j
public class BusinessStaffInfoController {

    @Resource
    private IBusinessStaffInfoService businessStaffInfoService;

    @Operation(summary = "创建-业务员信息")
    @PostMapping("/create")
    public CommonResult<Long> createBusinessStaffInfo(@Valid @RequestBody BusinessStaffInfoSaveDTO businessStaffInfoSave) {
        return success(businessStaffInfoService.saveBusinessStaffInfo(businessStaffInfoSave));
    }

    @Operation(summary = "获取-业务员列表下拉框")
    @PostMapping("/getPullDown")
    public CommonResult<PageResult<BusinessStaffInfoPullDownVo>> getBusinessStaffInfoPullDown(@Valid @RequestBody BusinessStaffInfoPullDownDTO businessStaffInfoPullDown) {
        return success(businessStaffInfoService.getBusinessStaffInfoPullDown(businessStaffInfoPullDown));
    }

    @Operation(summary = "获取-业务员分页列表")
    @PostMapping("/page")
    public CommonResult<PageResult<BusinessStaffPageVo>> getBusinessStaffPageList(@Valid @RequestBody BusinessStaffInfoQueryDTO businessStaffInfoQuery) {
        return success(businessStaffInfoService.getBusinessStaffPageList(businessStaffInfoQuery));
    }

    @Operation(summary = "编辑-业务员状态")
    @PostMapping("/updateStatus")
    public CommonResult<Boolean> updateBusinessStaffStatus(@Valid @RequestBody BusinessStaffInfoUpdateStatusDTO businessStaffInfoUpdate) {
        businessStaffInfoService.updateBusinessStaffStatus(businessStaffInfoUpdate);
        return success(true);
    }

    @Operation(summary = "获取-业务员范围详情")
    @PostMapping("/getScopeDetails")
    public CommonResult<BusinessStaffScopeDetailVo> getBusinessStaffScopeDetails(@RequestParam("id") Long id) {
        return success(businessStaffInfoService.getBusinessStaffScopeDetails(id));
    }

    @Operation(summary = "修改-业务员范围详情")
    @PostMapping("/updateScopeDetails")
    public CommonResult<Boolean> updateBusinessStaffScopeDetails(@Valid @RequestBody BusinessStaffInfoUpdateDTO businessStaffInfoUpdate) {
        businessStaffInfoService.updateBusinessStaffScopeDetails(businessStaffInfoUpdate);
        return success(true);
    }

}
