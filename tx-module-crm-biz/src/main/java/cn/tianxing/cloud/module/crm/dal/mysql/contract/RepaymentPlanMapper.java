package cn.tianxing.cloud.module.crm.dal.mysql.contract;

import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.tianxing.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tianxing.cloud.module.crm.controller.admin.contract.dto.RepaymentPlanQueryDTO;
import cn.tianxing.cloud.module.crm.controller.admin.repayment.vo.RepaymentPlanPageReqVO;
import cn.tianxing.cloud.module.crm.controller.admin.repayment.vo.RepaymentPlanRespVO;
import cn.tianxing.cloud.module.crm.dal.dataobject.contract.RepaymentPlanDO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【repayment_plan(还款计划表)】的数据库操作Mapper
* @createDate 2025-05-24 10:16:14
* @Entity cn.tianxing.cloud.module.crm.dal.dataobject.contract.RepaymentPlanDO
*/
@Mapper
public interface RepaymentPlanMapper extends BaseMapperX<RepaymentPlanDO> {

    default PageResult<RepaymentPlanDO> selectPage(RepaymentPlanQueryDTO query) {
        LambdaQueryWrapperX<RepaymentPlanDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eqIfPresent(RepaymentPlanDO::getContractId, query.getContractId())
                    .eqIfPresent(RepaymentPlanDO::getStatusCode, query.getStatusCode())
                    .betweenIfPresent(RepaymentPlanDO::getDueDate, query.getDueDate())
                    .orderByDesc(RepaymentPlanDO::getUpdateTime);

        return selectPage(query, queryWrapper);
    }

    /**
     * 查询还款计划分页列表
     *
     * @param page
     * @param reqVO 查询条件
     * @return 还款计划分页列表
     */
    List<RepaymentPlanRespVO> selectRepaymentPlanPage(Page<RepaymentPlanRespVO> page, @Param("reqVO") RepaymentPlanPageReqVO reqVO);

    void deleteByContractId(@Param("contractId") Long id, @Param("userId") Long userId);

    List<RepaymentPlanDO> getDayOfMonth(@Param("memberId") Long memberId, @Param("yearMonth") String yearMonth);
}
