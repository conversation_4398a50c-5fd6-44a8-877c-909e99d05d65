package cn.tianxing.cloud.module.crm.controller.admin.contract;

import cn.tianxing.cloud.framework.common.pojo.CommonResult;
import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.module.crm.controller.admin.contract.dto.CaseEntrustQueryDTO;
import cn.tianxing.cloud.module.crm.controller.admin.contract.dto.ContractEnterDTO;
import cn.tianxing.cloud.module.crm.controller.admin.contract.dto.RepaymentPlanDTO;
import cn.tianxing.cloud.module.crm.controller.admin.contract.test.User;
import cn.tianxing.cloud.module.crm.controller.admin.contract.test.UserServiceA;
import cn.tianxing.cloud.module.crm.controller.admin.contract.vo.CaseEntrustVO;
import cn.tianxing.cloud.module.crm.controller.admin.contract.vo.ContractInfoVO;
import cn.tianxing.cloud.module.crm.controller.admin.contract.vo.ContractMoneyCalculateVO;
import cn.tianxing.cloud.module.crm.controller.admin.contract.vo.RepaymentPlanVO;
import cn.tianxing.cloud.module.crm.dal.dataobject.contract.ContractBasicInfoDO;
import cn.tianxing.cloud.module.crm.service.caseentrust.CaseEntrustService;
import cn.tianxing.cloud.module.crm.service.contract.ContractBasicInfoService;
import cn.tianxing.cloud.module.crm.service.contract.RepaymentPlanService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.tianxing.cloud.framework.common.pojo.CommonResult.success;

/**
 * <p>
 * 合同录入 前端控制器
 * </p>
 */
@Tag(name = "管理后台 - 合同录入")
@RestController
@RequestMapping("/crm/contract/enter")
@Validated
public class ContractEnterController {

    @Resource
    private CaseEntrustService caseEntrustService;

    @Resource
    private ContractBasicInfoService contractBasicInfoService;

    @Resource
    private RepaymentPlanService repaymentPlanService;

    @Resource(name = "userServiceA")
    private UserServiceA userServiceA;

    @GetMapping("/testDataSources")
    @Operation(summary = "测试多数据源连接")
    @PermitAll
    public CommonResult<String> testDataSources() {
        try {
            StringBuilder result = new StringBuilder();
            
            // 测试A数据库连接并插入数据
            User user = new User();
            user.setName("tph");
            userServiceA.save(user);
            result.append("A数据库测试成功：id=").append(user.getId()).append(", name=").append(user.getName()).append("\n");

            // 测试B数据库连接并查询数据
            List<ContractBasicInfoDO> contracts = contractBasicInfoService.list();
            result.append("B数据库测试成功：查询到").append(contracts.size()).append("条合同数据");
            
            return success(result.toString());
        } catch (Exception e) {
            return CommonResult.error(500, "测试失败: " + e.getMessage());
        }
    }

    @PostMapping("/caseEntrust/page")
    @Operation(summary = "获取未签约案件")
    public CommonResult<PageResult<CaseEntrustVO>> getCaseEntrustPage(@Valid @RequestBody CaseEntrustQueryDTO caseEntrustQueryDTO) {
        return success(caseEntrustService.getCaseEntrustPage(caseEntrustQueryDTO));
    }

    @GetMapping("/caseEntrust/info/{id}")
    @Operation(summary = "获取委案详情")
    public CommonResult<CaseEntrustVO> getCaseEntrust(@PathVariable Long id) {
        return success(caseEntrustService.getCaseEntrust(id));
    }

    @PostMapping("/save")
    @Operation(summary = "录入合同")
    public CommonResult<Long> saveContract(@Valid @RequestBody ContractEnterDTO contractEnterDTO) throws Exception {
        Long contractId = contractBasicInfoService.saveContract(contractEnterDTO);
        return success(contractId);
    }

    @PostMapping("/calculateMoney")
    @Operation(summary = "根据委案id和期数获取相关金额")
    public CommonResult<ContractMoneyCalculateVO> calculateMoney(@RequestBody ContractMoneyCalculateVO calculateVO) {
        return success(contractBasicInfoService.calculateMoney(calculateVO.getCaseEntrustId(), calculateVO.getDebtReductionPeriods()));
    }

    @GetMapping("/queryContract/{id}")
    @Operation(summary = "合同基础信息")
    public CommonResult<ContractInfoVO> queryContract(@PathVariable Long id) {
        return success(contractBasicInfoService.queryContract(id));
    }

    @PostMapping("/queryRepaymentPlanPage")
    @Operation(summary = "合同id获取还款计划")
    public CommonResult<PageResult<RepaymentPlanVO>> queryRepaymentPlan(@RequestBody RepaymentPlanDTO repaymentPlanDTO) {
        return success(repaymentPlanService.queryRepaymentPlan(repaymentPlanDTO));
    }
}
