package cn.tianxing.cloud.module.crm.controller.admin.feedback.dto;

import cn.tianxing.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "反馈分页查询请求参数")
public class FeedbackPageReqDTO extends PageParam {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "类型 字典")
    private String type;

    @Schema(description = "反馈账号")
    private String account;

    @Schema(description = "处理状态 0未处理 1已处理")
    private Boolean dealStatus;

    @Schema(description = "处理人userId")
    private Long dealId;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "反馈开始时间")
    private String startTime;

    @Schema(description = "反馈结束时间")
    private String endTime;

    @Schema(description = "系统回复")
    private String systemRevert;


    @Schema(description = "处理人")
    private String handler;

    @Schema(description = "处理开始时间")
    private String dealStartTime;

    @Schema(description = "处理结束时间")
    private String dealEndTime;



}