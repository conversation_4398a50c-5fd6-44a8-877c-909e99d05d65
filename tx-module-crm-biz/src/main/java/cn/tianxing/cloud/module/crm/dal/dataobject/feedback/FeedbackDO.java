package cn.tianxing.cloud.module.crm.dal.dataobject.feedback;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "建议反馈表")
@TableName("feedback")
public class FeedbackDO {


    private static final long serialVersionUID = 1L;
    
    @Schema(description = "主键")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    
    @Schema(description = "类型 字典")
    private String type;
    
    @Schema(description = "反馈建议用户id")
    private Long feedbackUserId;
    
    @Schema(description = "反馈时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime feedbackTime;
    
    @Schema(description = "问题描述")
    private String problem;
    
    @Schema(description = "系统回复")
    private String systemRevert;
    
    @Schema(description = "联系方式")
    private String contact;
    
    @Schema(description = "处理状态 0未处理 1已处理")
    private Boolean dealStatus;
    
    @Schema(description = "处理人userId")
    private Long dealId;
    
    @Schema(description = "处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime dealTime;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    
    @Schema(description = "创建人")
    private String creator;
    
    @Schema(description = "删除标志（0：未删除，1：已删除）")
    @TableLogic
    private Boolean deleted;
} 