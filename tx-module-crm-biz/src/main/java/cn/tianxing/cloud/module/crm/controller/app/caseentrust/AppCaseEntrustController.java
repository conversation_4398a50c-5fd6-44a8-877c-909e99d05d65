package cn.tianxing.cloud.module.crm.controller.app.caseentrust;

import cn.tianxing.cloud.framework.common.pojo.CommonResult;
import cn.tianxing.cloud.module.crm.controller.app.caseentrust.vo.AppCaseEntrustQueryDTO;
import cn.tianxing.cloud.module.crm.controller.app.caseentrust.vo.BusinessStaffVO;
import cn.tianxing.cloud.module.crm.controller.app.caseentrust.vo.DebtorCaseEntrustVO;
import cn.tianxing.cloud.module.crm.controller.app.debt.vo.ContractVO;
import cn.tianxing.cloud.module.crm.service.caseentrust.CaseEntrustService;
import cn.tianxing.cloud.module.crm.service.contract.ContractBasicInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.tianxing.cloud.framework.common.pojo.CommonResult.success;

/**
 * <p>
 * 业务员信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Tag(name = "APP - 业务员管理")
@RestController
@RequestMapping("/crm/business")
@Validated
@Slf4j
public class AppCaseEntrustController {

    @Resource
    private CaseEntrustService caseEntrustService;

    @Resource
    private ContractBasicInfoService contractBasicInfoService;

    @Operation(summary = "获取客户列表")
    @PostMapping("/list")
    public CommonResult<List<BusinessStaffVO>> getPageList(@Valid @RequestBody AppCaseEntrustQueryDTO businessStaffInfoQuery) {
        return success(caseEntrustService.getBusinessStaffCustomerInfo(businessStaffInfoQuery));
    }

    @PostMapping("/debtor")
    @Operation(summary = "获得债务人所有委案信息")
    public CommonResult<DebtorCaseEntrustVO> getDebtorIdCollec(@Valid @RequestBody AppCaseEntrustQueryDTO dto) {
        return success(caseEntrustService.getDebtorIdCollec(dto));
    }

    @GetMapping("/case/{caseEntrustId}")
    @Operation(summary = "根据委案id查询合同信息")
    public CommonResult<ContractVO> getContractVOByCase(@PathVariable("caseEntrustId")Long caseEntrustId) {
        return success(contractBasicInfoService.getContractVOByCase(caseEntrustId));
    }
}
