package cn.tianxing.cloud.module.crm.controller.admin.test;

import cn.tianxing.cloud.framework.common.pojo.CommonResult;
import cn.tianxing.cloud.module.system.api.user.UserIdApi;
import cn.tianxing.cloud.module.system.api.user.vo.UserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.ZoneId;
import java.util.List;
import java.util.Map;


@Tag(name = "管理后台 - 测试")
@RestController
@RequestMapping("/test")
@Validated
public class TestController {

    private static final Logger logger = LoggerFactory.getLogger(TestController.class);

    @Resource
    private UserIdApi userIdApi;

    @PostMapping("/zone")
    @Operation(summary = "查询系统时区")
    @PermitAll
    public CommonResult<ZoneId> createUser2() {
        ZoneId defaultZone = ZoneId.systemDefault();
        return CommonResult.success(defaultZone);
    }

    @PostMapping("/userId")
    @Operation(summary = "测试userId")
    @PermitAll
    public CommonResult<Map<Long, UserVO>> getUserInfo(@RequestBody List<Long> userIds) {
        logger.info("接收到的userIds: {}", userIds);
        Map<Long, UserVO> userMapByIds = userIdApi.getUserMapByIds(userIds);
        for (Long userId : userIds) {
            UserVO userVO = userMapByIds.get(userId);
            if (userVO != null) {
                logger.info("userId {} 的userName是: {}", userId, userVO.getUsername());
            }
        }
        return CommonResult.success(userMapByIds);
    }
}
