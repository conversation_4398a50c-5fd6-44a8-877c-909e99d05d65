package cn.tianxing.cloud.module.crm.convert.caseentrust;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import cn.tianxing.cloud.module.crm.dal.dataobject.caseentrust.CaseEntrustAttachmentCategoryDO;
import cn.tianxing.cloud.module.crm.controller.admin.caseentrust.vo.CaseEntrustAttachmentCategoryVO;
import org.mapstruct.Mapping;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;

@Mapper
public interface CaseEntrustAttachmentCategoryConvert {

    CaseEntrustAttachmentCategoryConvert INSTANCE = Mappers.getMapper(CaseEntrustAttachmentCategoryConvert.class);

    /**
     * 单结构
     *
     * @param d
     * @return
     */
    @Mapping(target = "children", ignore = true)
    CaseEntrustAttachmentCategoryVO fromDO(CaseEntrustAttachmentCategoryDO d);

    /**
     * 树级结构转换
     *
     * @param list
     * @return
     */
    default List<CaseEntrustAttachmentCategoryVO> buildTree(List<CaseEntrustAttachmentCategoryVO> list) {
        Map<Long, CaseEntrustAttachmentCategoryVO> map = list.stream().collect(Collectors.toMap(CaseEntrustAttachmentCategoryVO::getId, v -> v));
        List<CaseEntrustAttachmentCategoryVO> roots = new ArrayList<>();
        for (CaseEntrustAttachmentCategoryVO node : list) {
            if (node.getParentId() == null || node.getParentId() == 0) {
                roots.add(node);
            } else {
                CaseEntrustAttachmentCategoryVO parent = map.get(node.getParentId());
                if (parent != null) {
                    parent.getChildren().add(node);
                }
            }
        }
        return roots;
    }
}
