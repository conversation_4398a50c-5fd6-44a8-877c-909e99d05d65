package cn.tianxing.cloud.module.crm.service.user;

import cn.tianxing.cloud.module.crm.dal.dataobject.user.MallUserMappingDO;

/**
 * 商城用户映射 Service 接口
 */
public interface MallUserMappingService {

    /**
     * 根据手机号查询商城用户映射
     *
     * @param phone 手机号
     * @return 商城用户映射
     */
    MallUserMappingDO getMallUserMappingByPhone(String phone);

    /**
     * 创建商城用户映射
     *
     * @param mallMemberId 商城会员ID
     * @param userMemberId 账管会员ID
     * @param phone 手机号
     * @return 商城用户映射
     */
    MallUserMappingDO createMallUserMapping(Long mallMemberId, Long userMemberId, String phone);
} 