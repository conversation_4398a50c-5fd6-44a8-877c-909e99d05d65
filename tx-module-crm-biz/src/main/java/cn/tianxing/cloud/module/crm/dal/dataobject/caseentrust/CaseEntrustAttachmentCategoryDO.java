package cn.tianxing.cloud.module.crm.dal.dataobject.caseentrust;

import cn.tianxing.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 委案附件分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("case_entrust_attachment_category")
public class CaseEntrustAttachmentCategoryDO extends BaseDO {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 父级ID（顶级为0）
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 分类名称
     */
    @TableField("name")
    private String name;

    /**
     * 分类编码
     */
    @TableField("code")
    private String code;

    /**
     * 排序号
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

}
