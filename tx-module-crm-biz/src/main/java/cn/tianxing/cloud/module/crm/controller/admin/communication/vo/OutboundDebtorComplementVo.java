package cn.tianxing.cloud.module.crm.controller.admin.communication.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台-外呼中心-债务人详情-信息补全VO")
@Data
public class OutboundDebtorComplementVo {

    @Schema(description = "补全信息ID")
    private Long id;

    @Schema(description = "修补字段")
    private String supplementField;

    @Schema(description = "修补内容")
    private String supplementContent;

    @Schema(description = "信息状态")
    private String infoStatus;

    @Schema(description = "操作人")
    private String updaterName;

    @Schema(description = "操作时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class) // 序列化（响应）
    @JsonDeserialize(using = LocalDateDeserializer.class) // 反序列化（请求）
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

}
