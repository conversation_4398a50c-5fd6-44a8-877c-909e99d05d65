package cn.tianxing.cloud.module.crm.dal.mysql.policydistribution;

import cn.tianxing.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.tianxing.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tianxing.cloud.module.crm.controller.admin.policydistribution.dto.BusinessStaffInfoQueryDTO;
import cn.tianxing.cloud.module.crm.controller.admin.policydistribution.vo.BusinessStaffPageVo;
import cn.tianxing.cloud.module.crm.dal.dataobject.policydistribution.BusinessStaffInfoDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 业务员信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Mapper
public interface BusinessStaffInfoMapper extends BaseMapperX<BusinessStaffInfoDO> {

    /**
     * 查询-业务员列表
     *
     * @param accountType   账号类型（来自字典 business_staff_account_type）
     * @param accountStatus 账号状态（来自字典 business_staff_account_status）
     * @return 集合列表
     */
    default List<BusinessStaffInfoDO> selectBusinessStaffInfoPullDown(String accountType, String accountStatus) {
        return selectList(new LambdaQueryWrapperX<BusinessStaffInfoDO>()
                .eqIfPresent(BusinessStaffInfoDO::getAccountType, accountType)
                .eqIfPresent(BusinessStaffInfoDO::getAccountStatus, accountStatus)
        );
    }

    /**
     * 查询-业务员分页列表
     *
     * @param page                   分页参数
     * @param businessStaffInfoQuery 分页条件
     * @return 分页列表
     */
    IPage<BusinessStaffPageVo> selectBusinessStaffPageList(IPage<BusinessStaffPageVo> page, BusinessStaffInfoQueryDTO businessStaffInfoQuery);

}
