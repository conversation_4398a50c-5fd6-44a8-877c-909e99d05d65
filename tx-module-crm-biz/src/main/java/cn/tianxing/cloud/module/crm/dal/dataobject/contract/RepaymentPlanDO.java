package cn.tianxing.cloud.module.crm.dal.dataobject.contract;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 还款计划表
 *
 * @TableName repayment_plan
 */
@TableName(value = "repayment_plan")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RepaymentPlanDO implements Serializable {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 关联合同ID
     */
    @Schema(description = "关联合同ID")
    private Long contractId;

    /**
     * 当前期数（如：1）
     */
    @Schema(description = "当前期数（如：1）")
    private Integer periodSeq;

    /**
     * 总期数（如：54）
     */
    @Schema(description = "总期数（如：54）")
    private Integer totalPeriods;

    /**
     * 应还款日期（格式：YYYY-MM-DD）
     */
    @Schema(description = "应还款日期（格式：YYYY-MM-DD）")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    private Date dueDate;

    /**
     * 计划还款本金
     */
    @Schema(description = "计划还款本金")
    private BigDecimal principal;


    @Schema(description = "实际化债金额")
    private BigDecimal actualDebtAmount;

    @Schema(description = "未结清金额")
    private BigDecimal outstandingAmount;

    /**
     * 利息
     */
    @Schema(description = "利息")
    private BigDecimal interest;

    /**
     * 还款状态（1-待还款，2-已结清，3-进行中，4-未结清）
     */
    @Schema(description = "还款状态（1-待还款 2-已结清 3-进行中 4-未结清）")
    private Integer statusCode;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
//    @JsonSerialize(using = LocalDateTimeSerializer.class)
//    @JsonDeserialize(using = LocalDateDeserializer.class)
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
//    @JsonSerialize(using = LocalDateTimeSerializer.class)
//    @JsonDeserialize(using = LocalDateDeserializer.class)
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    @TableField(value = "updater", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    /**
     * 删除标志（0：未删除，1：已删除）
     */
    @Schema(description = "删除标志（0：未删除，1：已删除）")
    @TableField("deleted")
    @TableLogic
    private Boolean deleted;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    @TableField("tenant_id")
    private Long tenantId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}