package cn.tianxing.cloud.module.crm.config;

import cn.tianxing.cloud.module.system.api.reduction.ReductionAmountConfigApi;
import cn.tianxing.cloud.module.system.api.user.AdminUserApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * CRM 使用到 Feign 的配置项
 */
@Configuration
@EnableFeignClients(clients = {
        ReductionAmountConfigApi.class,
        AdminUserApi.class
})
public class CrmRpcAutoConfiguration {
} 