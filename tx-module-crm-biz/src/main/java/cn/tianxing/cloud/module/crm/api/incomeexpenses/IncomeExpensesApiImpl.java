package cn.tianxing.cloud.module.crm.api.incomeexpenses;

import cn.tianxing.cloud.framework.common.pojo.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

import static cn.tianxing.cloud.framework.common.pojo.CommonResult.success;

/**
 * 收支明细 Api 接口
 */
@RestController
@Validated
public class IncomeExpensesApiImpl implements IncomeExpensesApi {

    @Override
    public CommonResult<BigDecimal> getMyCommission(Long memberUserId) {
        return success(BigDecimal.ZERO);
    }
}
