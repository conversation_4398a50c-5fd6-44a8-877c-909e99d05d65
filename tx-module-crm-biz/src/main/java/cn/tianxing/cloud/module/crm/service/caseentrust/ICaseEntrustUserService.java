package cn.tianxing.cloud.module.crm.service.caseentrust;

import cn.tianxing.cloud.module.crm.dal.dataobject.caseentrust.CaseEntrustUserDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 委案信息用户关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
public interface ICaseEntrustUserService extends IService<CaseEntrustUserDO> {

    /**
     * 获取委案用户关联信息
     *
     * @param caseEntrustIds 委案ID集合
     * @return
     */
    List<CaseEntrustUserDO> getCaseEntrustUserByCaseEntrustId(List<Long> caseEntrustIds);
}
