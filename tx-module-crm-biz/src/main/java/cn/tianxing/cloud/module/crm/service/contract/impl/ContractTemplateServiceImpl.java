package cn.tianxing.cloud.module.crm.service.contract.impl;

import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.module.crm.controller.admin.contract.dto.ContractTemplateQueryDTO;
import cn.tianxing.cloud.module.crm.controller.admin.contract.vo.ContractTemplateVO;
import cn.tianxing.cloud.module.crm.convert.contract.TemplateConvert;
import cn.tianxing.cloud.module.crm.dal.dataobject.contract.ContractTemplateDO;
import cn.tianxing.cloud.module.crm.dal.dataobject.contract.ContractTemplateExtDO;
import cn.tianxing.cloud.module.crm.dal.mysql.contract.ContractTemplateMapper;
import cn.tianxing.cloud.module.crm.service.contract.ContractTemplateExtService;
import cn.tianxing.cloud.module.crm.service.contract.ContractTemplateService;
import cn.tianxing.cloud.module.system.api.user.AdminUserApi;
import cn.tianxing.cloud.module.system.api.user.dto.AdminUserRespDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.tianxing.cloud.framework.security.core.util.SecurityFrameworkUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【contract_template(合同模板主表)】的数据库操作Service实现
 * @createDate 2025-05-24 10:16:14
 */
@Service
public class ContractTemplateServiceImpl extends ServiceImpl<ContractTemplateMapper, ContractTemplateDO>
        implements ContractTemplateService {

    @Resource
    private ContractTemplateExtService contractTemplateExtService;

    @Resource
    private AdminUserApi adminUserApi;

    @Override
    public PageResult<ContractTemplateVO> selectPage(ContractTemplateQueryDTO templateQueryDTO) {
        PageResult<ContractTemplateDO> doPage = this.baseMapper.selectPage(templateQueryDTO);
        List<ContractTemplateVO> templateVOList = TemplateConvert.INSTANCE.convertList(doPage.getList());
        //填充修改人
        List<Long> userIds = templateVOList.stream()
                .map(ContractTemplateVO::getUpdater)
                .map(Long::valueOf) // 将 String 转换为 Long
                .collect(Collectors.toList());
        List<AdminUserRespDTO> adminUsers = adminUserApi.getUserList(userIds).getCheckedData();
        Map<Long, String> userMap = adminUsers.stream()
                .collect(Collectors.toMap(
                        AdminUserRespDTO::getId,
                        AdminUserRespDTO::getNickname
                ));
        templateVOList.forEach(item -> {
            item.setUpdater(userMap.get(Long.valueOf(item.getUpdater())));
        });
        return new PageResult<>(templateVOList, doPage.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveTemplate(ContractTemplateDO template) {
        // 设置创建人和更新人
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        template.setCreator(String.valueOf(userId));
        template.setUpdater(String.valueOf(userId));

        // 保存主表
        this.save(template);

        // 保存附加信息
        if (template.getExtList() != null && !template.getExtList().isEmpty()) {
            List<ContractTemplateExtDO> extList = template.getExtList().stream()
                    .map(ext -> {
                        ContractTemplateExtDO extDO = new ContractTemplateExtDO();
                        extDO.setTemplateId(template.getId());
                        extDO.setFilePath(ext.getFilePath());
                        // 设置创建人和更新人
                        extDO.setCreator(String.valueOf(userId));
                        extDO.setUpdater(String.valueOf(userId));
                        return extDO;
                    })
                    .collect(Collectors.toList());
            contractTemplateExtService.saveBatch(extList);
        }

        return template.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeTemplate(Long id) {
        // 删除主表（逻辑删除）
        boolean result = this.removeById(id);
        if (result) {
            // 删除附加表（逻辑删除）
            LambdaQueryWrapper<ContractTemplateExtDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ContractTemplateExtDO::getTemplateId, id);
            contractTemplateExtService.remove(wrapper);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTemplate(ContractTemplateDO template) {
        // 设置更新人
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        template.setUpdater(String.valueOf(userId));

        // 更新主表
        boolean result = this.updateById(template);
        if (result && template.getExtList() != null) {
            // 先删除原有的附加信息（逻辑删除）
            LambdaQueryWrapper<ContractTemplateExtDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ContractTemplateExtDO::getTemplateId, template.getId());
            contractTemplateExtService.remove(wrapper);

            // 保存新的附加信息
            if (!template.getExtList().isEmpty()) {
                List<ContractTemplateExtDO> extList = template.getExtList().stream()
                        .map(ext -> {
                            ContractTemplateExtDO extDO = new ContractTemplateExtDO();
                            extDO.setTemplateId(template.getId());
                            extDO.setFilePath(ext.getFilePath());
                            // 设置创建人和更新人
                            extDO.setCreator(String.valueOf(userId));
                            extDO.setUpdater(String.valueOf(userId));
                            return extDO;
                        })
                        .collect(Collectors.toList());
                contractTemplateExtService.saveBatch(extList);
            }
        }
        return result;
    }

    @Override
    public ContractTemplateDO getTemplateDetail(Long id) {
        // 查询主表信息
        ContractTemplateDO template = this.getById(id);
        if (template != null) {
            // 查询附加信息
            LambdaQueryWrapper<ContractTemplateExtDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ContractTemplateExtDO::getTemplateId, id);
            List<ContractTemplateExtDO> extList = contractTemplateExtService.list(wrapper);
            template.setExtList(extList);
        }
        return template;
    }
}




