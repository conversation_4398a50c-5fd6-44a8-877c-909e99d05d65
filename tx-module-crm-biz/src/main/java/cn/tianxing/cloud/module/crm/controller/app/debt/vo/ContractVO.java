package cn.tianxing.cloud.module.crm.controller.app.debt.vo;

import cn.tianxing.cloud.module.crm.dal.dataobject.contract.ContractBasicInfoDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Schema(description = "app - 化债合同响应")
@Data
@ToString(callSuper = true)
public class ContractVO extends ContractBasicInfoDO {

    @Schema(description = "剩余化债金额")
    private BigDecimal remainingDebtAmount;

    @Schema(description = "合同地址")
    private String contractUrl;

    @Schema(description = "债权人")
    private String creditorName;

    @Schema(description = "逾期账号")
    private String overdueAccount;

    @Schema(description = "委托金额")
    private BigDecimal entrustedAmount;

    @Schema(description = "利息")
    private BigDecimal interest;

    @Schema(description = "年化率")
    private BigDecimal annualizedRate;

    @Schema(description = "滞纳金")
    private BigDecimal lateFee;

    @Schema(description = "最大减免金额")
    private BigDecimal maxReduction;


    @Schema(description = "当前期")
    private Integer currentPeriod;

    @Schema(description = "案件委托ID")
    private Long caseEntrustId;


    @Schema(description = "开卡银行")
    private String bank;

}
