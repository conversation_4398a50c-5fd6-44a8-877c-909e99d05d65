package cn.tianxing.cloud.module.crm.dal.mysql.user;

import cn.tianxing.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.tianxing.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tianxing.cloud.module.crm.dal.dataobject.user.MallUserMappingDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 商城用户映射 Mapper
 */
@Mapper
public interface MallUserMappingMapper extends BaseMapperX<MallUserMappingDO> {

    /**
     * 根据手机号查询商城用户映射
     *
     * @param phone 手机号
     * @return 商城用户映射
     */
    default MallUserMappingDO selectByPhone(String phone) {
        return selectOne(new LambdaQueryWrapperX<MallUserMappingDO>()
                .eq(MallUserMappingDO::getPhone, phone));
    }

    /**
     * 根据商城会员ID查询商城用户映射
     *
     * @param mallMemberId 商城会员ID
     * @return 商城用户映射
     */
    default MallUserMappingDO selectByMallMemberId(Long mallMemberId) {
        return selectOne(new LambdaQueryWrapperX<MallUserMappingDO>()
                .eq(MallUserMappingDO::getMallMemberId, mallMemberId));
    }
} 