package cn.tianxing.cloud.module.crm.service.policydistribution.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.tianxing.cloud.framework.common.pojo.CommonResult;
import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.framework.common.util.object.BeanUtils;
import cn.tianxing.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tianxing.cloud.module.crm.controller.admin.contract.vo.ContractNumberVO;
import cn.tianxing.cloud.module.crm.controller.admin.policydistribution.dto.*;
import cn.tianxing.cloud.module.crm.controller.admin.policydistribution.vo.BusinessStaffInfoPullDownVo;
import cn.tianxing.cloud.module.crm.controller.admin.policydistribution.vo.BusinessStaffPageVo;
import cn.tianxing.cloud.module.crm.controller.admin.policydistribution.vo.BusinessStaffScopeDetailVo;
import cn.tianxing.cloud.module.crm.dal.dataobject.policydistribution.BusinessStaffInfoDO;
import cn.tianxing.cloud.module.crm.dal.dataobject.policydistribution.BusinessStaffScopeDetailDO;
import cn.tianxing.cloud.module.crm.dal.mysql.policydistribution.BusinessStaffInfoMapper;
import cn.tianxing.cloud.module.crm.enums.DictTypeConstants;
import cn.tianxing.cloud.module.crm.enums.ErrorCodeConstants;
import cn.tianxing.cloud.module.crm.enums.contract.CrmContractSignStatusEnum;
import cn.tianxing.cloud.module.crm.service.contract.ContractBasicInfoService;
import cn.tianxing.cloud.module.crm.service.policydistribution.IBusinessStaffInfoService;
import cn.tianxing.cloud.module.crm.service.policydistribution.IBusinessStaffScopeDetailService;
import cn.tianxing.cloud.module.member.api.user.MemberUserApi;
import cn.tianxing.cloud.module.member.api.user.dto.MemberUserRespDTO;
import cn.tianxing.cloud.module.system.api.user.AdminUserApi;
import cn.tianxing.cloud.module.system.api.user.dto.AdminUserRespDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static cn.tianxing.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * <p>
 * 业务员信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Service
public class BusinessStaffInfoServiceImpl extends ServiceImpl<BusinessStaffInfoMapper, BusinessStaffInfoDO> implements IBusinessStaffInfoService {

    @Resource
    private MemberUserApi memberUserApi;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private ContractBasicInfoService contractBasicInfoService;

    @Resource
    private IBusinessStaffScopeDetailService businessStaffScopeDetailService;

    /**
     * 创建-业务员信息
     *
     * @param businessStaffInfoSave 创建信息
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveBusinessStaffInfo(BusinessStaffInfoSaveDTO businessStaffInfoSave) {

        // 创建业务员信息
        BusinessStaffInfoDO businessStaffInfo = BeanUtils.toBean(businessStaffInfoSave, BusinessStaffInfoDO.class);
        businessStaffInfo.setAccountStatus(DictTypeConstants.CRM_BUSINESS_STAFF_ENABLED);
        baseMapper.insert(businessStaffInfo);

        // 获取创建业务员地址信息
        Long businessInfoId = businessStaffInfo.getId();
        Long businessStaffId = businessStaffInfo.getBusinessStaffId();
        List<String> businessScopeDetail = businessStaffInfoSave.getBusinessScopeDetail();

        // 创建业务员地址明细
        saveBusinessStaffScopeDetail(businessScopeDetail, businessInfoId, businessStaffId);

        // 返回业务员信息ID
        return businessStaffInfo.getId();
    }

    /**
     * 业务员地址明细
     *
     * @param businessScopeDetail 业务员地址明细
     * @param businessInfoId      业务信息ID
     * @param businessStaffId     业务员ID
     */
    private void saveBusinessStaffScopeDetail(List<String> businessScopeDetail, Long businessInfoId, Long businessStaffId) {
        List<BusinessStaffScopeDetailDO> businessStaffScopeDetailList = new ArrayList<>();
        Map<String, String> provinceMap = new HashMap<>();
        Map<String, String> cityMap = new HashMap<>();

        if (CollectionUtil.isNotEmpty(businessScopeDetail)) {

            // 自然排序
            businessScopeDetail.sort(Comparator.naturalOrder());

            // 设置业务员地址明细
            for (String addrCode : businessScopeDetail) {
                if ("0000".equals(addrCode.substring(addrCode.length() - 4))) {
                    // 省
                    String code = addrCode.substring(0, 2);
                    provinceMap.put(code, addrCode);
                } else if ("00".equals(addrCode.substring(addrCode.length() - 2))) {
                    // 市
                    String code = addrCode.substring(0, 2);
                    cityMap.put(code, addrCode);
                } else {
                    // 区
                    BusinessStaffScopeDetailDO businessStaffScopeDetailDO = new BusinessStaffScopeDetailDO();
                    String code = addrCode.substring(0, 2);
                    businessStaffScopeDetailDO.setProvinceCode(provinceMap.get(code));
                    businessStaffScopeDetailDO.setCityCode(cityMap.get(code));
                    businessStaffScopeDetailDO.setAreaCode(addrCode);
                    businessStaffScopeDetailDO.setBusinessStaffId(businessStaffId);
                    businessStaffScopeDetailDO.setBusinessInfoId(businessInfoId);
                    businessStaffScopeDetailList.add(businessStaffScopeDetailDO);
                }
            }

            // 创建业务员地址明细
            businessStaffScopeDetailService.saveBatch(businessStaffScopeDetailList);
        }
    }

    /**
     * 获取-业务员列表下拉框
     *
     * @param @param businessStaffInfoPullDown 分页条件
     * @return
     */
    @Override
    public PageResult<BusinessStaffInfoPullDownVo> getBusinessStaffInfoPullDown(BusinessStaffInfoPullDownDTO businessStaffInfoPullDown) {

        // 根据账号类型查询业务员列表
        String accountType = businessStaffInfoPullDown.getAccountType();
        List<BusinessStaffInfoDO> businessStaffInfoList = baseMapper.selectBusinessStaffInfoPullDown(accountType, DictTypeConstants.CRM_BUSINESS_STAFF_ENABLED);

        // 收集已创建的业务员ID集合
        List<Long> businessStaffIdList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(businessStaffInfoList)) {
            businessStaffIdList = businessStaffInfoList.stream().map(BusinessStaffInfoDO::getBusinessStaffId).collect(Collectors.toList());
        }

        // 组装Vo
        List<BusinessStaffInfoPullDownVo> businessStaffInfoPullDownList = new ArrayList<>();
        Long total = 0L;
        Integer pageSize = businessStaffInfoPullDown.getPageSize();
        Integer pageNo = businessStaffInfoPullDown.getPageNo();
        if (accountType.equals(DictTypeConstants.CRM_BUSINESS_STAFF_EXTERNAL_MEMBER)) {
            // 外部会员
            CommonResult<PageResult<MemberUserRespDTO>> commonResult = memberUserApi.getFilterUsers(pageNo, pageSize, businessStaffIdList);
            List<MemberUserRespDTO> memberUserList = commonResult.getData().getList();
            total = commonResult.getData().getTotal();
            if (CollectionUtil.isNotEmpty(memberUserList)) {
                for (MemberUserRespDTO memberUserRespDTO : memberUserList) {
                    BusinessStaffInfoPullDownVo businessStaffInfoPullDownVo = new BusinessStaffInfoPullDownVo();
                    businessStaffInfoPullDownVo.setBusinessStaffId(memberUserRespDTO.getId());
                    businessStaffInfoPullDownVo.setBusinessStaffName(memberUserRespDTO.getNickname());
                    businessStaffInfoPullDownVo.setBusinessStaffUsername(memberUserRespDTO.getUsername());
                    businessStaffInfoPullDownList.add(businessStaffInfoPullDownVo);
                }
            }
        } else if (accountType.equals(DictTypeConstants.CRM_BUSINESS_STAFF_INTERNAL_STAFF)) {
            // 内部员工
            CommonResult<PageResult<AdminUserRespDTO>> commonResult = adminUserApi.getFilterUserList(pageNo, pageSize, businessStaffIdList);
            List<AdminUserRespDTO> adminUserList = commonResult.getData().getList();
            total = commonResult.getData().getTotal();
            if (CollectionUtil.isNotEmpty(adminUserList)) {
                for (AdminUserRespDTO adminUserRespDTO : adminUserList) {
                    BusinessStaffInfoPullDownVo businessStaffInfoPullDownVo = new BusinessStaffInfoPullDownVo();
                    businessStaffInfoPullDownVo.setBusinessStaffId(adminUserRespDTO.getId());
                    businessStaffInfoPullDownVo.setBusinessStaffName(adminUserRespDTO.getNickname());
                    businessStaffInfoPullDownVo.setBusinessStaffUsername(adminUserRespDTO.getUsername());
                    businessStaffInfoPullDownList.add(businessStaffInfoPullDownVo);
                }
            }
        } else {
            // 异常处理
            throw exception(ErrorCodeConstants.BUSINESS_STAFF_ACCOUNT_TYPE_NOT_EXISTS, accountType);
        }

        // 返回Vo
        return new PageResult<>(businessStaffInfoPullDownList, total);
    }

    /**
     * 获取-业务员分页列表
     *
     * @param businessStaffInfoQuery 分页查询条件
     * @return 分页列表
     */
    @Override
    public PageResult<BusinessStaffPageVo> getBusinessStaffPageList(BusinessStaffInfoQueryDTO businessStaffInfoQuery) {

        // 分页查询
        IPage<BusinessStaffPageVo> page = new Page<>(businessStaffInfoQuery.getPageNo(), businessStaffInfoQuery.getPageSize());
        baseMapper.selectBusinessStaffPageList(page, businessStaffInfoQuery);

        // 数据组装
        List<BusinessStaffPageVo> businessStaffPageList = page.getRecords();
        if (CollectionUtil.isNotEmpty(businessStaffPageList)) {
            // 创建 外部会员、内部员工ID、修改人ID集合
            List<Long> externalMemberIds = new ArrayList<>();
            List<Long> internalStaffIds = new ArrayList<>();
            List<Long> updaterIds = new ArrayList<>();

            // 收集ID集合
            for (BusinessStaffPageVo businessStaffPage : businessStaffPageList) {
                String accountType = businessStaffPage.getAccountType();
                if (accountType.equals(DictTypeConstants.CRM_BUSINESS_STAFF_EXTERNAL_MEMBER)) {
                    // 外部会员
                    externalMemberIds.add(businessStaffPage.getBusinessStaffId());
                } else {
                    // 内部员工
                    internalStaffIds.add(businessStaffPage.getBusinessStaffId());
                }
                // 修改人
                if (StrUtil.isNotBlank(businessStaffPage.getUpdater())) {
                    updaterIds.add(Long.valueOf(businessStaffPage.getUpdater()));
                }
            }

            // 获取修改人名称Map
            Map<Long, AdminUserRespDTO> userMap = adminUserApi.getUserMap(updaterIds);

            // 业务员分页信息补充
            BusinessStaffPageReplenish(businessStaffPageList, externalMemberIds, internalStaffIds, userMap);

        }
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 编辑-业务员状态
     *
     * @param businessStaffInfoUpdate 编辑信息
     * @return
     */
    @Override
    public void updateBusinessStaffStatus(BusinessStaffInfoUpdateStatusDTO businessStaffInfoUpdate) {

        // 校验业务员信息是否存在
        Long id = businessStaffInfoUpdate.getId();
        BusinessStaffInfoDO businessStaffInfo = baseMapper.selectById(id);
        if (Objects.isNull(businessStaffInfo)) {
            throw exception(ErrorCodeConstants.BUSINESS_STAFF_NOT_EXISTS, "不存在");
        }

        // 修改业务员状态
        businessStaffInfo.setAccountStatus(businessStaffInfoUpdate.getAccountStatus());
        baseMapper.updateById(businessStaffInfo);
    }

    /**
     * 获取-业务员是否存在
     *
     * @param businessStaffId 业务员ID
     * @return
     */
    @Override
    public Boolean getBusinessStaffExist(Long businessStaffId) {
        // 查询业务员信息
        BusinessStaffInfoDO businessStaffInfo = baseMapper.selectOne(new LambdaQueryWrapperX<BusinessStaffInfoDO>().eqIfPresent(BusinessStaffInfoDO::getBusinessStaffId, businessStaffId));

        // 如果查询结果为空，则返回 false
        if (Objects.isNull(businessStaffInfo)) {
            return false;
        } else {
            // 如果查询结果不为空，则判断业务员状态是否为启用状态
            if (businessStaffInfo.getAccountStatus().equals(DictTypeConstants.CRM_BUSINESS_STAFF_ENABLED)) {
                return true;
            } else {
                return false;
            }
        }
    }

    /**
     * 获取-业务员范围详情
     *
     * @param id 主键ID
     * @return
     */
    @Override
    public BusinessStaffScopeDetailVo getBusinessStaffScopeDetails(Long id) {
        // 业务信息校验
        BusinessStaffInfoDO businessStaffInfo = getById(id);
        if (Objects.isNull(businessStaffInfo)) {
            throw exception(ErrorCodeConstants.BUSINESS_STAFF_NOT_EXISTS, "不存在");
        }

        // 数据拷贝
        BusinessStaffScopeDetailVo businessStaffScopeDetail = BeanUtils.toBean(businessStaffInfo, BusinessStaffScopeDetailVo.class);

        // 获取业务员范围详情
        List<String> businessStaffScopeDetailList = businessStaffScopeDetailService.getBusinessStaffScopeDetailList(businessStaffScopeDetail.getId());
        businessStaffScopeDetail.setBusinessScopeDetail(businessStaffScopeDetailList);

        // 返回Vo
        return businessStaffScopeDetail;
    }

    /**
     * 修改-业务员范围详情
     *
     * @param businessStaffInfoUpdate 修改信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBusinessStaffScopeDetails(BusinessStaffInfoUpdateDTO businessStaffInfoUpdate) {

        // 业务信息校验
        BusinessStaffInfoDO businessStaffInfo = getById(businessStaffInfoUpdate.getId());
        if (Objects.isNull(businessStaffInfo)) {
            throw exception(ErrorCodeConstants.BUSINESS_STAFF_NOT_EXISTS, "不存在");
        }

        // 修改业务员信息
        businessStaffInfo.setBusinessScope(businessStaffInfoUpdate.getBusinessScope());
        baseMapper.updateById(businessStaffInfo);

        // 修改业务员范围详情
        // 删除原来已存在的业务员范围详情
        LambdaQueryWrapperX<BusinessStaffScopeDetailDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(BusinessStaffScopeDetailDO::getBusinessInfoId, businessStaffInfoUpdate.getId());
        businessStaffScopeDetailService.getBaseMapper().delete(lambdaQueryWrapperX);

        // 添加业务员范围详情
        List<String> businessScopeDetail = businessStaffInfoUpdate.getBusinessScopeDetail();
        Long businessStaffInfoId = businessStaffInfo.getId();
        Long businessStaffId = businessStaffInfo.getBusinessStaffId();
        saveBusinessStaffScopeDetail(businessScopeDetail, businessStaffInfoId, businessStaffId);
    }

    /**
     * 业务员分页信息补充
     *
     * @param businessStaffPageList 分页列表
     * @param externalMemberIds     外部会员ID集合
     * @param internalStaffIds      内部员工ID集合
     * @param userMap               修改人Map
     */
    private void BusinessStaffPageReplenish(List<BusinessStaffPageVo> businessStaffPageList, List<Long> externalMemberIds, List<Long> internalStaffIds, Map<Long, AdminUserRespDTO> userMap) {

        // 准备Map
        Map<Long, Integer> externalUnsignedMap = new HashMap<>();
        Map<Long, Integer> externalSignedMap = new HashMap<>();
        Map<Long, Integer> internalUnsignedMap = new HashMap<>();
        Map<Long, Integer> internalSignedMap = new HashMap<>();

        // 获取外部员工合同数量
        if (CollectionUtil.isNotEmpty(externalMemberIds)) {
            // 未签订
            List<ContractNumberVO> externalUnsigneList = contractBasicInfoService.getContractNumber(externalMemberIds, null);
            if (CollectionUtil.isNotEmpty(externalUnsigneList)) {
                externalUnsignedMap = externalUnsigneList.stream().collect(Collectors.toMap(ContractNumberVO::getSalesman, ContractNumberVO::getNumber));
            }
            // 已签订
            List<ContractNumberVO> externalSignedList = contractBasicInfoService.getContractNumber(externalMemberIds, CrmContractSignStatusEnum.SIGNED.getStatus());
            if (CollectionUtil.isNotEmpty(externalSignedList)) {
                externalSignedMap = externalUnsigneList.stream().collect(Collectors.toMap(ContractNumberVO::getSalesman, ContractNumberVO::getNumber));
            }
        }

        // 获取内部员工合同数量
        if (CollectionUtil.isNotEmpty(internalStaffIds)) {
            // 未签订
            List<ContractNumberVO> internalUnsignedList = contractBasicInfoService.getContractNumber(internalStaffIds, null);
            if (CollectionUtil.isNotEmpty(internalUnsignedList)) {
                internalUnsignedMap = internalUnsignedList.stream().collect(Collectors.toMap(ContractNumberVO::getSalesman, ContractNumberVO::getNumber));
            }
            // 已签订
            List<ContractNumberVO> internalSignedList = contractBasicInfoService.getContractNumber(internalStaffIds, CrmContractSignStatusEnum.SIGNED.getStatus());
            if (CollectionUtil.isNotEmpty(internalSignedList)) {
                internalSignedMap = internalUnsignedList.stream().collect(Collectors.toMap(ContractNumberVO::getSalesman, ContractNumberVO::getNumber));
            }
        }

        // 分页信息补充
        for (BusinessStaffPageVo businessStaffPageVo : businessStaffPageList) {

            // 基础信息
            Long businessStaffId = businessStaffPageVo.getBusinessStaffId();
            String accountType = businessStaffPageVo.getAccountType();
            String updater = businessStaffPageVo.getUpdater();

            // 合同信息
            if (accountType.equals(DictTypeConstants.CRM_BUSINESS_STAFF_EXTERNAL_MEMBER)) {
                businessStaffPageVo.setResponsibleNumber(externalUnsignedMap.get(businessStaffId) == null ? 0 : externalUnsignedMap.get(businessStaffId));
                businessStaffPageVo.setFinishNumber(externalSignedMap.get(businessStaffId) == null ? 0 : externalSignedMap.get(businessStaffId));
            } else {
                businessStaffPageVo.setResponsibleNumber(internalUnsignedMap.get(businessStaffId) == null ? 0 : internalUnsignedMap.get(businessStaffId));
                businessStaffPageVo.setFinishNumber(internalSignedMap.get(businessStaffId) == null ? 0 : internalSignedMap.get(businessStaffId));
            }

            // 佣金数暂且处理、默认为0
            businessStaffPageVo.setCommissionAmount(BigDecimal.ZERO);

            // 修改人名称
            if (StrUtil.isNotBlank(updater)) {
                AdminUserRespDTO adminUserRespDTO = userMap.get(Long.valueOf(updater));
                if (Objects.nonNull(adminUserRespDTO)) {
                    businessStaffPageVo.setUpdaterName(adminUserRespDTO.getUsername());
                }
            }

        }

    }

}
