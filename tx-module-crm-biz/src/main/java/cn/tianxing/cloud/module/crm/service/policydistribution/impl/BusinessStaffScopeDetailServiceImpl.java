package cn.tianxing.cloud.module.crm.service.policydistribution.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.tianxing.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tianxing.cloud.module.crm.dal.dataobject.policydistribution.BusinessStaffScopeDetailDO;
import cn.tianxing.cloud.module.crm.dal.mysql.policydistribution.BusinessStaffScopeDetailMapper;
import cn.tianxing.cloud.module.crm.service.policydistribution.IBusinessStaffScopeDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Service
public class BusinessStaffScopeDetailServiceImpl extends ServiceImpl<BusinessStaffScopeDetailMapper, BusinessStaffScopeDetailDO> implements IBusinessStaffScopeDetailService {

    /**
     * App-获取业务员ID
     *
     * @param addressCodes 省市区地址编号
     * @return
     */
    @Override
    public Long getBusinessStaffId(ArrayList<String> addressCodes) {

        // 空处理
        int size = addressCodes.size();
        if (size == 0) {
            return null;
        }

        // 省市区变量
        Long provinceCode = null;
        Long cityCode = null;
        Long areaCode = null;

        // 变量赋值
        for (String addrCode : addressCodes) {
            if ("0000".equals(addrCode.substring(addrCode.length() - 4))) {
                // 省
                provinceCode = Long.valueOf(addrCode);
            } else if ("00".equals(addrCode.substring(addrCode.length() - 2))) {
                // 市
                cityCode = Long.valueOf(addrCode);
            } else {
                // 区
                areaCode = Long.valueOf(addrCode);
            }
        }

        // 返回业务员ID
        List<BusinessStaffScopeDetailDO> businessStaffScopeDetailList = baseMapper.selectBusinessStaffList(provinceCode, cityCode, areaCode);
        if (CollectionUtil.isEmpty(businessStaffScopeDetailList)) {
            return null;
        } else {
            return businessStaffScopeDetailList.get(0).getBusinessStaffId();
        }

    }

    /**
     * 获取业务员范围集合
     *
     * @param businessInfoId 业务员主键ID
     */
    @Override
    public List<String> getBusinessStaffScopeDetailList(Long businessInfoId) {

        // 条件查询业务员范围详情
        LambdaQueryWrapperX<BusinessStaffScopeDetailDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eqIfPresent(BusinessStaffScopeDetailDO::getBusinessInfoId, businessInfoId);
        List<BusinessStaffScopeDetailDO> businessStaffScopeDetailList = baseMapper.selectList(lambdaQueryWrapperX);

        // 获取业务员范围集合
        Set<String> businessStaffScopeDetail = new HashSet<>();
        if (CollectionUtil.isNotEmpty(businessStaffScopeDetailList)) {
            for (BusinessStaffScopeDetailDO businessStaffScopeDetailDO : businessStaffScopeDetailList) {
                businessStaffScopeDetail.add(businessStaffScopeDetailDO.getProvinceCode());
                businessStaffScopeDetail.add(businessStaffScopeDetailDO.getCityCode());
                businessStaffScopeDetail.add(businessStaffScopeDetailDO.getAreaCode());
            }
        }

        // 返回业务员范围集合
        return new ArrayList<>(businessStaffScopeDetail);
    }


}
