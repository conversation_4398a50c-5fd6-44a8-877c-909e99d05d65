package cn.tianxing.cloud.module.crm.convert.contract;

import cn.tianxing.cloud.framework.common.util.collection.CollectionUtils;
import cn.tianxing.cloud.framework.common.util.object.BeanUtils;
import cn.tianxing.cloud.module.crm.controller.admin.contract.dto.ContractTemplateExtSaveDTO;
import cn.tianxing.cloud.module.crm.controller.admin.contract.dto.ContractTemplateSaveDTO;
import cn.tianxing.cloud.module.crm.controller.admin.contract.vo.ContractTemplateVO;
import cn.tianxing.cloud.module.crm.dal.dataobject.contract.ContractTemplateDO;
import cn.tianxing.cloud.module.crm.dal.dataobject.contract.ContractTemplateExtDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.stream.Collectors;

@Mapper
public interface TemplateConvert {

    TemplateConvert INSTANCE = Mappers.getMapper(TemplateConvert.class);

    /**
     * 批量转换
     * @param list 合同模板列表
     * @return 
     */
    default List<ContractTemplateVO> convertList(List<ContractTemplateDO> list) {
        return CollectionUtils.convertList(list, this::convert);
    }

    /**
     * 单独转换
     * @param collectDO 合同模板
     * @return
     */
    default ContractTemplateVO convert(ContractTemplateDO collectDO) {
        return BeanUtils.toBean(collectDO, ContractTemplateVO.class);
    }

    /**
     * 单独转换
     * @param dto 合同模板保存DTO
     * @return
     */
    default ContractTemplateDO convert(ContractTemplateSaveDTO dto) {
        ContractTemplateDO templateDO = BeanUtils.toBean(dto, ContractTemplateDO.class);
        if (dto.getExtList() != null) {
            List<ContractTemplateExtDO> extList = dto.getExtList().stream()
                .map(ext -> {
                    ContractTemplateExtDO extDO = new ContractTemplateExtDO();
                    extDO.setFilePath(ext.getFilePath());
                    return extDO;
                })
                .collect(Collectors.toList());
            templateDO.setExtList(extList);
        }
        return templateDO;
    }

}
