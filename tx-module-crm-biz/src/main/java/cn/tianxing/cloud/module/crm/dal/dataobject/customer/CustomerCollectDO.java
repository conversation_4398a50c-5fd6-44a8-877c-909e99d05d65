package cn.tianxing.cloud.module.crm.dal.dataobject.customer;

import cn.tianxing.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 信息收集表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("customer_collect")
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerCollectDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 客户编号
     */
    @TableField("customer_num")
    private String customerNum;

    /**
     * 客户姓名
     */
    @TableField("customer_name")
    private String customerName;

    /**
     * 省
     */
    @TableField("address_province")
    private String addressProvince;

    /**
     * 市
     */
    @TableField("address_city")
    private String addressCity;

    /**
     * 地址编码
     */
    @TableField("address_code")
    private String addressCode;

    /**
     * 地址
     */
    @TableField("address_name")
    private String addressName;

    /**
     * 地址详情
     */
    @TableField("address_detail")
    private String addressDetail;

    /**
     * 类型 (来自字典)
     */
    @TableField("type")
    private String type;

    /**
     * 债权/债务金额（来自字典）
     */
    @TableField("money")
    private String money;

    /**
     * 债务/债权类型（来自字典）
     */
    @TableField("money_type")
    private String moneyType;

    /**
     * 客户性别（来自字典）
     */
    @TableField("customer_sex")
    private Integer customerSex;

    /**
     * 客户联系方式
     */
    @TableField("customer_phone")
    private String customerPhone;

    /**
     * 客户身份证号
     */
    @TableField("customer_id_card")
    private String customerIdCard;

    /**
     * 年龄
     */
    @TableField("age")
    private Integer age;

    /**
     * 信用卡开卡-省
     */
    @TableField("card_province")
    private String cardProvince;

    /**
     * 信用卡开卡-市
     */
    @TableField("card_city")
    private String cardCity;

    /**
     * 信用卡相关银行
     */
    @TableField("card_code")
    private String cardCode;

    /**
     * 信用卡其他
     */
    @TableField("other")
    private String other;

    /**
     * 信用卡开卡城市
     */
    @TableField("card_addr")
    private String cardAddr;

    /**
     * 信用卡开卡城市名称
     */
    @TableField("card_addr_name")
    private String cardAddrName;

    /**
     * 补充说明
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField(value = "creator",fill = FieldFill.INSERT)
    private String creator;

    /**
     * 更新人
     */
    @TableField(value = "updater",fill = FieldFill.INSERT_UPDATE)
    private String updater;

    /**
     * 删除标志（0：未删除，1：已删除）
     */
    @TableField("deleted")
    private Boolean deleted;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;


}
