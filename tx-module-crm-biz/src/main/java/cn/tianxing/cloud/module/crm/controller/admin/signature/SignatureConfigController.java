package cn.tianxing.cloud.module.crm.controller.admin.signature;

import cn.tianxing.cloud.framework.common.pojo.CommonResult;
import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.module.crm.controller.admin.signature.dto.SignatureConfigQueryDTO;
import cn.tianxing.cloud.module.crm.controller.admin.signature.dto.SignatureConfigSaveDTO;
import cn.tianxing.cloud.module.crm.controller.admin.signature.vo.SignatureConfigVO;
import cn.tianxing.cloud.module.crm.convert.signature.SignatureConfigConvert;
import cn.tianxing.cloud.module.crm.dal.dataobject.signature.SignatureConfigDO;
import cn.tianxing.cloud.module.crm.service.signature.SignatureConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.tianxing.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 签字配置管理")
@RestController
@RequestMapping("/crm/signature")
@Validated
public class SignatureConfigController {

    @Resource
    private SignatureConfigService signatureConfigService;

    @PostMapping("/page")
    @Operation(summary = "获得签字分页列表")
    public CommonResult<PageResult<SignatureConfigVO>> selectSignature(@Valid @RequestBody SignatureConfigQueryDTO queryDTO) {
        return success(signatureConfigService.selectPage(queryDTO));
    }

    @PostMapping("create")
    @Operation(summary = "新增签字")
    public CommonResult<Long> createSignature(@RequestBody SignatureConfigSaveDTO signature) {
        SignatureConfigDO signatureDO = SignatureConfigConvert.INSTANCE.convert(signature);
        signatureConfigService.save(signatureDO);
        return success(signature.getId());
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除签字")
    public CommonResult<Boolean> deleteSignature(@PathVariable Long id) {
        return success(signatureConfigService.removeById(id));
    }

    @PutMapping("/update")
    @Operation(summary = "更新签字")
    public CommonResult<Boolean> updateSignature(@RequestBody SignatureConfigSaveDTO signature) {
        SignatureConfigDO signatureDO = SignatureConfigConvert.INSTANCE.convert(signature);
        return success(signatureConfigService.updateById(signatureDO));
    }
} 