package cn.tianxing.cloud.module.crm.service.repayment.impl;

import cn.tianxing.cloud.module.crm.controller.app.earnings.dto.AppEarningsQueryDTO;
import cn.tianxing.cloud.module.crm.controller.app.earnings.vo.AppEarningsCombinationVo;
import cn.tianxing.cloud.module.crm.controller.app.earnings.vo.AppEarningsInfoVo;
import cn.tianxing.cloud.module.crm.dal.dataobject.repayment.DebtRepaymentDetailsDO;
import cn.tianxing.cloud.module.crm.dal.mysql.repayment.DebtRepaymentDetailsMapper;
import cn.tianxing.cloud.module.crm.service.repayment.EarningsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Objects;

import static cn.tianxing.cloud.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

/**
 * 收益 服务实现
 */
@Service
public class EarningsServiceImpl extends ServiceImpl<DebtRepaymentDetailsMapper, DebtRepaymentDetailsDO> implements EarningsService {

    /**
     * 查询-我的收益
     *
     * @param earningsQuery 查询条件
     * @return
     */
    @Override
    public AppEarningsInfoVo getMyEarnings(AppEarningsQueryDTO earningsQuery) {

        AppEarningsInfoVo earningsInfo = new AppEarningsInfoVo();
        Long memberUserId = earningsQuery.getMemberUserId();
        if (Objects.isNull(memberUserId)) {
            memberUserId = getLoginUserId();
        }
        Integer type = earningsQuery.getType();
        if (Objects.isNull(type)) {
            type = 0;
        }
        earningsInfo.setType(type);

        // 设置金额信息
        AppEarningsCombinationVo earningsCombination = new AppEarningsCombinationVo();
        // 可用收益
        BigDecimal sumIncome = baseMapper.selectSumMarningsMoney(memberUserId, 0);
        BigDecimal sumExpenses = baseMapper.selectSumMarningsMoney(memberUserId, 1);
        BigDecimal difference = sumIncome.subtract(sumExpenses);
        BigDecimal rounded = difference.setScale(2, RoundingMode.HALF_UP);
        earningsCombination.setSumEarnings(sumIncome.setScale(2, RoundingMode.HALF_UP));
        earningsCombination.setUsableEarnings(rounded);
        earningsCombination.setFreezeMoney(BigDecimal.ZERO);
        earningsCombination.setPendingSettlementMoney(BigDecimal.ZERO);
        earningsCombination.setCanWithdraw(BigDecimal.ZERO);
        earningsInfo.setEarningsCombination(earningsCombination);

        // 金额明细
        if (type == 0) {
            // 收入
            earningsInfo.setEarningsMoneyDetailList(baseMapper.getMonthEarningDetail(memberUserId));
        } else if (type == 1) {
            // 支出
            earningsInfo.setEarningsMoneyDetailList(new ArrayList<>());
        } else {
            // 待结算
            earningsInfo.setEarningsMoneyDetailList(new ArrayList<>());
        }

        return earningsInfo;
    }
}
