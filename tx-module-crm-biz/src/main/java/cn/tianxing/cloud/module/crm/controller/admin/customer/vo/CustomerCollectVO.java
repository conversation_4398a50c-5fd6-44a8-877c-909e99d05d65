package cn.tianxing.cloud.module.crm.controller.admin.customer.vo;

import cn.tianxing.cloud.module.crm.controller.admin.customer.dto.CustomerCollectSaveDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 客户信息收集返回参数")
@Data
public class CustomerCollectVO extends CustomerCollectSaveDTO {

    @Schema(description = "客户编号")
    private String customerNum;

    @Schema(description = "收集人")
    private String creatorName;

    @Schema(description = "收集时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "是否显示查看按钮 显示:true 隐藏:false")
    private Boolean displayQueryButton;

    @Schema(description = "是否显示修改按钮 显示:true 隐藏:false")
    private Boolean displayUpdateButton;

    @Schema(description = "是否显示删除按钮 显示:true 隐藏:false")
    private Boolean displayDeleteButton;

}
