package cn.tianxing.cloud.module.crm.controller.app.caseentrust.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "APP - 账管顾问 - 返回结果")
@Data
public class BusinessStaffVO {

    @Schema(description = "委案数量")
    private Integer caseCount;

    @Schema(description = "债务人ID")
    private Long debtorId;

    @Schema(description = "债务人类型")
    private String debtorType;


    @Schema(description = "债务人证件类型")
    private String debtorIdType;

    @Schema(description = "债务人名称", example = "天行健科技有限公司")
    private String debtorName;

    @Schema(description = "统一社会信用代码/个人身份证号", example = "91110000802100433B")
    private String debtorCreditCode;

}
