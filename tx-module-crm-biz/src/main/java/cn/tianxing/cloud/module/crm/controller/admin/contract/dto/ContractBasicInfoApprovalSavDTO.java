package cn.tianxing.cloud.module.crm.controller.admin.contract.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;


@Schema(description = "管理后台 - 合同审批参数")
@Data
public class ContractBasicInfoApprovalSavDTO {

    @Schema(description = "合同主键ID集合")
    @NotEmpty(message = "合同主键ID集合不能为空")
    private List<Long> ids;

    @NotNull(message = "合同状态不能为空")
    @Schema(description = "合同状态: 草稿 draft\n" +
            "审批中 in_approval\n" +
            "审批通过 end_approval\n" +
            "审批驳回 back_approval\n" +
            "作废 cancel\n" +
            "履约中 in_performance\n" +
            "合同结束 end\n" +
            "变更 change")
    private String status;

    @Schema(description = "审批描述")
    private String approvalRemark;

}
