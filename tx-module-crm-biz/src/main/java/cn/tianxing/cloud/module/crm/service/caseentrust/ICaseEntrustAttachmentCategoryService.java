package cn.tianxing.cloud.module.crm.service.caseentrust;

import cn.tianxing.cloud.module.crm.dal.dataobject.caseentrust.CaseEntrustAttachmentCategoryDO;
import com.baomidou.mybatisplus.extension.service.IService;
import cn.tianxing.cloud.module.crm.controller.admin.caseentrust.vo.CaseEntrustAttachmentCategoryVO;
import java.util.List;

/**
 * <p>
 * 委案附件分类表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
public interface ICaseEntrustAttachmentCategoryService extends IService<CaseEntrustAttachmentCategoryDO> {

    /**
     * 查询全部委案附件分类树
     */
    List<CaseEntrustAttachmentCategoryVO> getAttachmentCategoryTreeList();

}
