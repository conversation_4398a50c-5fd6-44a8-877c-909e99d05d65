package cn.tianxing.cloud.module.crm.framework.security.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "company")
public class ContracConfig {

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司法人
     */
    private String legalPerson;

    /**
     * 公司地址
     */
    private String address;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 社会信用代码
     */
    private String socialCreditCode;

    /**
     * 公司印章
     */
    private String companySeal;

    /**
     * 公司法人
     */
    private String companyUser;

    /**
     * 公司签订模板
     */
    private String companySignedTemplate;

    /**
     * 签订公司名称版本变量
     */
    private String signedCompanyName;

    /**
     * 签订名称模板变量
     */
    private String signedUserName;

    /**
     * 签订时间模板变量
     */
    private String signedTime;

    /**
     * 签订印章模板变量
     */
    private String signedSeal;

    /**
     * 丙方债务人签名
     */
    private String debtorSing;

    /**
     * 丙方债务人签名
     */
    private String debtorSingTwo;

    /**
     * 丙方债务人名称
     */
    private String debtorName;

    /**
     * 丙方债务人日期
     */
    private String debtorSingTime;

}
