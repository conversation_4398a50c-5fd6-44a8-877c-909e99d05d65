package cn.tianxing.cloud.module.crm.controller.admin.repayment.vo;

import cn.tianxing.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 合同还款计划分页查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RepaymentPlanPageReqVO extends PageParam {

    @Schema(description = "合同ID")
    private Long contractId;


    @Schema(description = "委案编号", example = "CASE12345")
    private String caseNumber;

    @Schema(description = "合同编号", example = "HT20230101")
    private String contractNumber;

    @Schema(description = "债务人姓名", example = "张三")
    private String debtorName;

    @Schema(description = "债务人身份证号", example = "110101199001011234")
    private String debtorIdNumber;
    
    @Schema(description = "债务人用户名", example = "user123")
    private String username;
    
    @Schema(description = "债务人昵称", example = "小张")
    private String nickname;
    
    @Schema(description = "债权人", example = "某某银行")
    private String creditor;

    @Schema(description = "合同状态", example = "SIGNED")
    private String status;

    @Schema(description = "开始日期范围开始", example = "2023-01-01")
    private String startDateBegin;

    @Schema(description = "开始日期范围结束", example = "2023-12-31")
    private String startDateEnd;
} 