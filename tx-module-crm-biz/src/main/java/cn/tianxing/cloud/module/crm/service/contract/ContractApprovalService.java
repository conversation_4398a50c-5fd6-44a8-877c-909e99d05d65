package cn.tianxing.cloud.module.crm.service.contract;

import cn.tianxing.cloud.module.crm.controller.admin.contract.dto.ContractBasicInfoApprovalSavDTO;
import cn.tianxing.cloud.module.crm.controller.admin.contract.vo.ContractApprovalInfoVO;
import cn.tianxing.cloud.module.crm.dal.dataobject.contract.ContractBasicInfoDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 合同审批Service
* @createDate 2025-05-24 10:16:14
*/
public interface ContractApprovalService extends IService<ContractBasicInfoDO> {

    /**
     * 批量合同状态
     * @param basicInfoApprovalSavDTO
     */
    void updateBatchContractStatus(ContractBasicInfoApprovalSavDTO basicInfoApprovalSavDTO);

    /**
     * 获取合同审批详情
     * @param id 合同ID
     * @return
     */
    ContractApprovalInfoVO selectApprovalInfo(Long id);
}
