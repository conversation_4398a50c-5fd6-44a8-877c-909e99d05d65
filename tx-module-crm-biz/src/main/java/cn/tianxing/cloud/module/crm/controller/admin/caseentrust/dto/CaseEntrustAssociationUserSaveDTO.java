package cn.tianxing.cloud.module.crm.controller.admin.caseentrust.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 委案关联用户DTO")
@Data
public class CaseEntrustAssociationUserSaveDTO {

    @Schema(description = "委案关联ID")
    private Long caseAssociationtId;

    @Schema(description = "委案ID")
    @NotNull(message = "委案ID不能为空")
    private Long id;

}
