package cn.tianxing.cloud.module.system.api.ip;

import cn.tianxing.cloud.framework.common.pojo.CommonResult;
import cn.tianxing.cloud.module.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - ip地址")
public interface IpApi {

    String PREFIX = ApiConstants.PREFIX + "/area";

    @GetMapping(PREFIX + "/getProvinces")
    @Operation(summary = "获取省市区地址")
    @Parameter(name = "areaCode", description = "区编号", example = "1024", required = true)
    CommonResult<String> getProvinces(@RequestParam(value = "provinceCode", required = false) String provinceCode,
                                      @RequestParam(value = "cityCode", required = false) String cityCode,
                                      @RequestParam(value = "areaCode", required = false) String areaCode);
}
