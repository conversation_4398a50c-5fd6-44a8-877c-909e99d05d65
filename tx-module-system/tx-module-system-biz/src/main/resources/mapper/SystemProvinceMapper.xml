<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.tianxing.cloud.module.system.dal.mysql.area.SystemProvinceMapper">

    <resultMap id="BaseResultMap" type="cn.tianxing.cloud.module.system.dal.dataobject.area.SystemProvinceDO">
            <id property="id" column="id" />
            <result property="name" column="name" />
            <result property="code" column="code" />
    </resultMap>

    <sql id="Base_Column_List">
        id,name,code
    </sql>
</mapper>
