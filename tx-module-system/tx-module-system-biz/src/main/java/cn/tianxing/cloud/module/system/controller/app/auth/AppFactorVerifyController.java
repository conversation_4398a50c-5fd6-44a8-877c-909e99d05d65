package cn.tianxing.cloud.module.system.controller.app.auth;

import cn.tianxing.cloud.framework.common.pojo.CommonResult;
import cn.tianxing.cloud.framework.common.util.servlet.ServletUtils;
import cn.tianxing.cloud.module.system.api.sms.dto.code.SmsCodeUseReqDTO;
import cn.tianxing.cloud.module.system.controller.admin.auth.vo.IdentityVerifyDTO;
import cn.tianxing.cloud.module.system.service.auth.FactorVerifyService;
import cn.tianxing.cloud.module.system.service.sms.SmsCodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/system/verify")
@Validated
@Tag(name = "用户APP - 实名三要素认证")
public class AppFactorVerifyController {

    @Resource
    private FactorVerifyService verifyService;

    @Resource
    private SmsCodeService smsCodeService;

    @PostMapping("/identity")
    @Operation(summary = "实名认证")
    public CommonResult<Boolean> verify(@Valid @RequestBody IdentityVerifyDTO verifyDTO) {

        // 创建使用请求
        SmsCodeUseReqDTO reqDTO = new SmsCodeUseReqDTO();
        reqDTO.setScene(5);
        reqDTO.setMobile(verifyDTO.getMobile());
        reqDTO.setCode(verifyDTO.getCode());
        reqDTO.setUsedIp(ServletUtils.getClientIP());
        // 使用验证码
        smsCodeService.useSmsCode(reqDTO);
        return verifyService.verifyIdentity(verifyDTO);
    }
}