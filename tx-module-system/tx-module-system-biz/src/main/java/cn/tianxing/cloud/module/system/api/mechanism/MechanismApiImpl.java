package cn.tianxing.cloud.module.system.api.mechanism;

import cn.tianxing.cloud.framework.common.pojo.CommonResult;
import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.framework.common.util.object.BeanUtils;
import cn.tianxing.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tianxing.cloud.framework.mybatis.core.util.MyBatisUtils;
import cn.tianxing.cloud.module.system.api.mechanism.dto.MechanismPageReqDTO;
import cn.tianxing.cloud.module.system.api.mechanism.dto.MechanismRespDTO;
import cn.tianxing.cloud.module.system.dal.dataobject.mechanism.MechanismDO;
import cn.tianxing.cloud.module.system.dal.mysql.mechanism.MechanismMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import static cn.tianxing.cloud.framework.common.pojo.CommonResult.success;

/**
 * 机构信息 API 实现类
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class MechanismApiImpl implements MechanismApi {

    @Resource
    private MechanismMapper mechanismMapper;

    @Override
    public CommonResult<PageResult<MechanismRespDTO>> getEnabledMechanismPage(MechanismPageReqDTO pageReqDTO) {
        // 构建查询条件
        LambdaQueryWrapper<MechanismDO> queryWrapper = new LambdaQueryWrapperX<MechanismDO>()
                .eq(MechanismDO::getStatus, 0) // 启用状态
                .likeIfPresent(MechanismDO::getMechanismName, pageReqDTO.getMechanismName())
                .eqIfPresent(MechanismDO::getMechanismType, pageReqDTO.getMechanismType())
                .select(MechanismDO::getId, MechanismDO::getMechanismName,
                        MechanismDO::getCreditCode, MechanismDO::getLegalPerson,
                        MechanismDO::getMechanismType, MechanismDO::getContactPhone,
                        MechanismDO::getIdType, MechanismDO::getJobTitle);
        
        // 执行分页查询
        Page<MechanismDO> page = mechanismMapper.selectPage(MyBatisUtils.buildPage(pageReqDTO), queryWrapper);
        
        // 转换为分页结果
        PageResult<MechanismRespDTO> pageResult = new PageResult<>();
        pageResult.setList(BeanUtils.toBean(page.getRecords(), MechanismRespDTO.class));
        pageResult.setTotal(page.getTotal());
        
        return success(pageResult);
    }
} 