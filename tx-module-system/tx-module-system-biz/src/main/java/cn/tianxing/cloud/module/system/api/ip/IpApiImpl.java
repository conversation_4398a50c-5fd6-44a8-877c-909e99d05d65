package cn.tianxing.cloud.module.system.api.ip;

import cn.hutool.core.util.StrUtil;
import cn.tianxing.cloud.framework.common.pojo.CommonResult;
import cn.tianxing.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tianxing.cloud.module.system.dal.dataobject.area.SystemAreaDO;
import cn.tianxing.cloud.module.system.dal.dataobject.area.SystemCityDO;
import cn.tianxing.cloud.module.system.dal.dataobject.area.SystemProvinceDO;
import cn.tianxing.cloud.module.system.service.area.SystemAreaService;
import cn.tianxing.cloud.module.system.service.area.SystemCityService;
import cn.tianxing.cloud.module.system.service.area.SystemProvinceService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class IpApiImpl implements IpApi {

    @Resource
    private SystemProvinceService systemProvinceService;

    @Resource
    private SystemCityService systemCityService;

    @Resource
    private SystemAreaService systemAreaService;

    @Override
    public CommonResult<String> getProvinces(String provinceCode, String cityCode, String areaCode) {
        StringBuffer sb = new StringBuffer();

        // 省
        if (StrUtil.isNotBlank(provinceCode)) {
            SystemProvinceDO provinceDO = systemProvinceService.getBaseMapper().selectOne(new LambdaQueryWrapperX<SystemProvinceDO>().eqIfPresent(SystemProvinceDO::getCode, provinceCode));
            if (Objects.nonNull(provinceDO)) {
                sb.append(provinceDO.getName());
            }
        }

        // 市
        if (StrUtil.isNotBlank(cityCode)) {
            SystemCityDO cityDO = systemCityService.getBaseMapper().selectOne(new LambdaQueryWrapperX<SystemCityDO>().eqIfPresent(SystemCityDO::getCode, cityCode));
            if (Objects.nonNull(cityDO)) {
                sb.append(cityDO.getName());
            }
        }

        // 区
        if (StrUtil.isNotBlank(areaCode)) {
            SystemAreaDO areaDO = systemAreaService.getBaseMapper().selectOne(new LambdaQueryWrapperX<SystemAreaDO>().eqIfPresent(SystemAreaDO::getCode, areaCode));
            if (Objects.nonNull(areaDO)) {
                sb.append(areaDO.getName());
            }
        }

        return CommonResult.success(sb.toString());
    }
}
