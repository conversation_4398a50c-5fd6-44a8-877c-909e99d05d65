package cn.tianxing.cloud.module.system.controller.admin.sms;

import cn.tianxing.cloud.framework.common.enums.UserTypeEnum;
import cn.tianxing.cloud.framework.common.pojo.CommonResult;
import cn.tianxing.cloud.framework.common.util.servlet.ServletUtils;
import cn.tianxing.cloud.framework.common.validation.InEnum;
import cn.tianxing.cloud.framework.security.core.LoginUser;
import cn.tianxing.cloud.framework.security.core.util.SecurityFrameworkUtils;
import cn.tianxing.cloud.module.system.api.sms.dto.code.SmsCodeSendReqDTO;
import cn.tianxing.cloud.module.system.api.sms.dto.code.SmsCodeUseReqDTO;
import cn.tianxing.cloud.module.system.api.sms.dto.code.SmsCodeValidateReqDTO;
import cn.tianxing.cloud.module.system.controller.admin.sms.vo.code.SmsCodeSendReqVO;
import cn.tianxing.cloud.module.system.enums.sms.SmsSceneEnum;
import cn.tianxing.cloud.module.system.service.member.MemberService;
import cn.tianxing.cloud.module.system.service.sms.SmsCodeService;
import cn.tianxing.cloud.module.system.service.user.AdminUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.tianxing.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 短信验证码")
@RestController
@RequestMapping("/system/sms/code")
@Validated
public class SmsCodeController {

    @Resource
    private SmsCodeService smsCodeService;

    @Resource
    private AdminUserService adminUserService;

    @Resource
    private MemberService memberService;

    @PostMapping("/send")
    @Operation(summary = "创建短信验证码，并进行发送")
    public CommonResult<Boolean> sendSmsCode(@Valid @RequestBody SmsCodeSendReqVO reqVO,
                                           HttpServletRequest request) {
        // 创建发送请求
        SmsCodeSendReqDTO reqDTO = new SmsCodeSendReqDTO();
        reqDTO.setScene(reqVO.getScene());
        reqDTO.setCreateIp(request.getRemoteAddr());

        // 如果手机号为空，则从当前登录用户获取
        if (reqVO.getMobile() == null) {
            LoginUser user = SecurityFrameworkUtils.getLoginUser();
            if (user != null && user.getUserType().equals(UserTypeEnum.ADMIN.getValue())) {
                String mobile = adminUserService.getUser(user.getId()).getMobile();
                reqDTO.setMobile(mobile);
            }
            if (user == null || user.getUserType().equals(UserTypeEnum.MEMBER.getValue())){
                String mobile = null;
                if (user != null) {
                    mobile = memberService.getMemberUserMobile(user.getId());
                }
                reqDTO.setMobile(mobile);
            }
        } else {
            reqDTO.setMobile(reqVO.getMobile());
        }

        // 发送验证码
        smsCodeService.sendSmsCode(reqDTO);
        return success(true);
    }

    @PutMapping("/use")
    @Operation(summary = "验证短信验证码，并进行使用")
    public CommonResult<Boolean> useSmsCode(@RequestParam("scene") @NotNull(message = "发送场景不能为空")
                                          @InEnum(SmsSceneEnum.class) Integer scene,
                                          @RequestParam("code") @NotNull(message = "验证码不能为空") String code,
                                          HttpServletRequest request) {
        // 创建使用请求
        SmsCodeUseReqDTO reqDTO = new SmsCodeUseReqDTO();
        reqDTO.setScene(scene);
        reqDTO.setCode(code);
        reqDTO.setUsedIp(ServletUtils.getClientIP());
        // 使用验证码
        smsCodeService.useSmsCode(reqDTO);
        return success(true);
    }

    @GetMapping("/validate")
    @Operation(summary = "检查验证码是否有效")
    public CommonResult<Boolean> validateSmsCode(@RequestParam("scene") @NotNull(message = "发送场景不能为空")
                                               @InEnum(SmsSceneEnum.class) Integer scene,
                                               @RequestParam("code") @NotNull(message = "验证码不能为空") String code) {
        // 创建校验请求
        SmsCodeValidateReqDTO reqDTO = new SmsCodeValidateReqDTO();
        reqDTO.setScene(scene);
        reqDTO.setCode(code);
        // 校验验证码
        smsCodeService.validateSmsCode(reqDTO);
        return success(true);
    }
} 