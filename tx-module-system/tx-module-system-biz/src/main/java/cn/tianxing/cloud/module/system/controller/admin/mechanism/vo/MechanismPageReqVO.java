package cn.tianxing.cloud.module.system.controller.admin.mechanism.vo;

import cn.tianxing.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.tianxing.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 机构分页查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MechanismPageReqVO extends PageParam {

    @Schema(description = "机构名称", example = "天行健科技有限公司")
    private String mechanismName;

    @Schema(description = "机构类型", example = "企业")
    private String mechanismType;

    @Schema(description = "证件类型", example = "身份证")
    private String idType;

    @Schema(description = "会员id", example = "1024")
    private Long memberId;

    @Schema(description = "统一社会信用代码", example = "91110000802100433B")
    private String creditCode;

    @Schema(description = "联系电话", example = "13800138000")
    private String contactPhone;

    @Schema(description = "联系座机", example = "010-12345678")
    private String contactTelephone;

    @Schema(description = "电子邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "职业", example = "工程师")
    private String profession;

    @Schema(description = "职务", example = "高级工程师")
    private String jobTitle;

    @Schema(description = "单位名称", example = "天行健科技有限公司")
    private String companyName;

    @Schema(description = "法定代表人", example = "张三")
    private String legalPerson;

    @Schema(description = "状态", example = "1")
    private Integer status;

    @Schema(description = "创建时间", example = "[2022-07-01 00:00:00,2022-07-01 23:59:59]")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
    
    @Schema(description = "是否有营业执照", example = "true")
    private Boolean hasBusinessLicense;
} 