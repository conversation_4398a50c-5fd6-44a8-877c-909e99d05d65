package cn.tianxing.cloud.module.system.service.mechanism;

import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.module.member.api.user.dto.MemberUserPageReqDTO;
import cn.tianxing.cloud.module.member.api.user.dto.MemberUserRespDTO;
import cn.tianxing.cloud.module.system.controller.admin.mechanism.vo.*;
import cn.tianxing.cloud.module.system.dal.dataobject.mechanism.MechanismDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 机构 Service 接口
 */
public interface MechanismService {

    /**
     * 创建机构
     *
     * @param createReqVO 创建信息
     * @return 机构编号
     */
    Long createMechanism(@Valid MechanismCreateReqVO createReqVO);

    /**
     * 更新机构
     *
     * @param updateReqVO 更新信息
     */
    void updateMechanism(@Valid MechanismUpdateReqVO updateReqVO);

    /**
     * 获取虚拟手机号
     *
     * @param realPhone 真实手机号
     * @return 虚拟手机号
     */
    String getVirtualPhone(String realPhone);

    /**
     * 删除机构
     *
     * @param id 机构编号
     */
    void deleteMechanism(Long id);

    /**
     * 获得机构
     *
     * @param id 机构编号
     * @return 机构
     */
    MechanismDO getMechanism(Long id);

    /**
     * 获得机构分页
     *
     * @param pageReqVO 分页查询
     * @return 机构分页
     */
    PageResult<MechanismRespVO> getMechanismPage(MechanismPageReqVO pageReqVO);

    List<ReductionAmountConfigVO> getReductionConfigList(Long mechanismId,Integer status);

    /**
     * 获得启用的会员用户分页
     *
     * @param pageReqDTO 分页查询
     * @return 会员用户分页
     */
    PageResult<MemberUserRespDTO> getEnabledMemberUsersPage(MemberUserPageReqDTO pageReqDTO);

}