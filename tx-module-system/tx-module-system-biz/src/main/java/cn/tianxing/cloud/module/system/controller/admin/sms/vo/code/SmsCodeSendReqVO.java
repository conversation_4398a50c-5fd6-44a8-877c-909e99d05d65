package cn.tianxing.cloud.module.system.controller.admin.sms.vo.code;

import cn.tianxing.cloud.framework.common.validation.InEnum;
import cn.tianxing.cloud.framework.common.validation.Mobile;
import cn.tianxing.cloud.module.system.enums.sms.SmsSceneEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

@Schema(description = "管理后台 - 短信验证码发送 Request VO")
@Data
public class SmsCodeSendReqVO {

    @Schema(description = "手机号", example = "15601691300")
    @Mobile
    private String mobile;

    @Schema(description = "发送场景", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "发送场景不能为空")
    @InEnum(SmsSceneEnum.class)
    private Integer scene;
} 